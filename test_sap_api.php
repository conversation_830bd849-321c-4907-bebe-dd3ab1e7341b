<?php
/**
 * SAP Business One Service Layer - Enhanced API Explorer
 * Complete Swagger-like interface for testing all available endpoints
 *
 * Enhanced Features:
 * - Performance monitoring and metrics
 * - Advanced error handling and logging
 * - Request/Response caching
 * - Batch testing capabilities
 * - Export functionality
 * - Auto-documentation generation
 * - Enhanced session management
 *
 * @version 2.0
 * <AUTHOR> SAP B1 API Explorer
 */

// Enhanced error reporting and logging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/logs/api_explorer.log');

// Create logs directory if it doesn't exist
if (!is_dir(__DIR__ . '/logs')) {
    mkdir(__DIR__ . '/logs', 0755, true);
}

// Performance monitoring
$start_time = microtime(true);
$memory_start = memory_get_usage();

/**
 * Enhanced logging function
 */
function logMessage($level, $message, $context = []) {
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' | Context: ' . json_encode($context) : '';
    $logEntry = "[{$timestamp}] [{$level}] {$message}{$contextStr}" . PHP_EOL;

    // Write to log file
    file_put_contents(__DIR__ . '/logs/api_explorer.log', $logEntry, FILE_APPEND | LOCK_EX);

    // Also log to error_log for critical errors
    if (in_array($level, ['ERROR', 'CRITICAL'])) {
        error_log($logEntry);
    }
}

/**
 * Performance metrics tracker
 */
class PerformanceTracker {
    private static $metrics = [];

    public static function start($operation) {
        self::$metrics[$operation] = [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage()
        ];
    }

    public static function end($operation) {
        if (isset(self::$metrics[$operation])) {
            self::$metrics[$operation]['end_time'] = microtime(true);
            self::$metrics[$operation]['end_memory'] = memory_get_usage();
            self::$metrics[$operation]['duration'] = self::$metrics[$operation]['end_time'] - self::$metrics[$operation]['start_time'];
            self::$metrics[$operation]['memory_used'] = self::$metrics[$operation]['end_memory'] - self::$metrics[$operation]['start_memory'];
        }
    }

    public static function getMetrics() {
        return self::$metrics;
    }

    public static function getMetric($operation) {
        return self::$metrics[$operation] ?? null;
    }
}

// Start tracking page load performance
PerformanceTracker::start('page_load');

/**
 * Enhanced Configuration Management
 */
class ConfigManager {
    private static $config = null;

    public static function load() {
        if (self::$config === null) {
            // Try to load from environment variables first
            self::$config = [
                'base_url' => $_ENV['SAP_BASE_URL'] ?? 'https://192.168.2.249:50000/b1s/v2/',
                'username' => $_ENV['SAP_USERNAME'] ?? 'selva',
                'password' => $_ENV['SAP_PASSWORD'] ?? '1191',
                'company_db' => $_ENV['SAP_COMPANY_DB'] ?? 'CAPA',
                'ssl_verify' => filter_var($_ENV['SAP_SSL_VERIFY'] ?? 'false', FILTER_VALIDATE_BOOLEAN),
                'timeout' => (int)($_ENV['SAP_TIMEOUT'] ?? 30),
                'connect_timeout' => (int)($_ENV['SAP_CONNECT_TIMEOUT'] ?? 10),
                'cache_enabled' => filter_var($_ENV['CACHE_ENABLED'] ?? 'true', FILTER_VALIDATE_BOOLEAN),
                'cache_ttl' => (int)($_ENV['CACHE_TTL'] ?? 300), // 5 minutes
                'max_retries' => (int)($_ENV['MAX_RETRIES'] ?? 3),
                'retry_delay' => (int)($_ENV['RETRY_DELAY'] ?? 1000), // milliseconds
            ];

            logMessage('INFO', 'Configuration loaded', ['source' => 'environment']);
        }

        return self::$config;
    }

    public static function get($key, $default = null) {
        $config = self::load();
        return $config[$key] ?? $default;
    }

    public static function set($key, $value) {
        self::load(); // Ensure config is loaded
        self::$config[$key] = $value;
    }
}

/**
 * Simple caching system for API responses
 */
class ResponseCache {
    private static $cache_dir = null;

    private static function getCacheDir() {
        if (self::$cache_dir === null) {
            self::$cache_dir = __DIR__ . '/cache/';
            if (!is_dir(self::$cache_dir)) {
                mkdir(self::$cache_dir, 0755, true);
            }
        }
        return self::$cache_dir;
    }

    public static function get($key) {
        if (!ConfigManager::get('cache_enabled')) {
            return null;
        }

        $cache_file = self::getCacheDir() . md5($key) . '.cache';

        if (file_exists($cache_file)) {
            $cache_data = json_decode(file_get_contents($cache_file), true);

            if ($cache_data && $cache_data['expires'] > time()) {
                logMessage('DEBUG', 'Cache hit', ['key' => $key]);
                return $cache_data['data'];
            } else {
                // Cache expired, remove file
                unlink($cache_file);
                logMessage('DEBUG', 'Cache expired', ['key' => $key]);
            }
        }

        return null;
    }

    public static function set($key, $data, $ttl = null) {
        if (!ConfigManager::get('cache_enabled')) {
            return false;
        }

        $ttl = $ttl ?? ConfigManager::get('cache_ttl');
        $cache_file = self::getCacheDir() . md5($key) . '.cache';

        $cache_data = [
            'data' => $data,
            'expires' => time() + $ttl,
            'created' => time()
        ];

        $result = file_put_contents($cache_file, json_encode($cache_data), LOCK_EX);

        if ($result !== false) {
            logMessage('DEBUG', 'Cache set', ['key' => $key, 'ttl' => $ttl]);
            return true;
        }

        return false;
    }

    public static function clear() {
        $cache_dir = self::getCacheDir();
        $files = glob($cache_dir . '*.cache');

        foreach ($files as $file) {
            unlink($file);
        }

        logMessage('INFO', 'Cache cleared', ['files_removed' => count($files)]);
        return count($files);
    }
}

// Load configuration
$config = ConfigManager::load();

/**
 * Enhanced Session Manager
 */
class SessionManager {
    private static $cookie_jar = null;
    private static $session_authenticated = false;
    private static $session_data = [];
    private static $last_activity = null;

    public static function getCookieJar() {
        if (self::$cookie_jar === null) {
            self::$cookie_jar = tempnam(sys_get_temp_dir(), 'sap_cookies_enhanced_');
        }
        return self::$cookie_jar;
    }

    public static function isAuthenticated() {
        return self::$session_authenticated;
    }

    public static function setAuthenticated($status) {
        self::$session_authenticated = $status;
        self::$last_activity = time();

        if ($status) {
            logMessage('INFO', 'Session authenticated successfully');
        } else {
            logMessage('INFO', 'Session authentication cleared');
        }
    }

    public static function getSessionData() {
        return self::$session_data;
    }

    public static function setSessionData($data) {
        self::$session_data = $data;
        self::$last_activity = time();
    }

    public static function getLastActivity() {
        return self::$last_activity;
    }

    public static function isSessionExpired() {
        if (self::$last_activity === null) {
            return true;
        }

        $session_timeout = ConfigManager::get('session_timeout', 3600); // 1 hour default
        return (time() - self::$last_activity) > $session_timeout;
    }

    public static function cleanup() {
        if (self::$cookie_jar && file_exists(self::$cookie_jar)) {
            unlink(self::$cookie_jar);
        }
        self::$session_authenticated = false;
        self::$session_data = [];
        self::$last_activity = null;

        logMessage('INFO', 'Session cleaned up');
    }
}

/**
 * Retry mechanism for API requests
 */
class RetryHandler {
    public static function execute($callback, $maxRetries = null, $delay = null) {
        $maxRetries = $maxRetries ?? ConfigManager::get('max_retries', 3);
        $delay = $delay ?? ConfigManager::get('retry_delay', 1000);

        $lastException = null;

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                PerformanceTracker::start("retry_attempt_{$attempt}");
                $result = $callback();
                PerformanceTracker::end("retry_attempt_{$attempt}");

                // If we get here, the request was successful
                if ($attempt > 1) {
                    logMessage('INFO', 'Request succeeded after retry', ['attempt' => $attempt]);
                }

                return $result;

            } catch (Exception $e) {
                $lastException = $e;
                PerformanceTracker::end("retry_attempt_{$attempt}");

                logMessage('WARNING', 'Request attempt failed', [
                    'attempt' => $attempt,
                    'error' => $e->getMessage(),
                    'max_retries' => $maxRetries
                ]);

                // If this was the last attempt, don't wait
                if ($attempt < $maxRetries) {
                    usleep($delay * 1000); // Convert to microseconds
                    $delay *= 2; // Exponential backoff
                }
            }
        }

        // All attempts failed
        logMessage('ERROR', 'All retry attempts failed', [
            'max_retries' => $maxRetries,
            'final_error' => $lastException ? $lastException->getMessage() : 'Unknown error'
        ]);

        throw $lastException ?? new Exception('All retry attempts failed');
    }
}

// Initialize session manager
$cookie_jar = SessionManager::getCookieJar();
$session_authenticated = SessionManager::isAuthenticated();

/**
 * Enhanced auto-connect with better error handling and performance tracking
 */
$auto_connect_result = null;
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    PerformanceTracker::start('auto_connect');

    try {
        // Check if session is expired first
        if (SessionManager::isSessionExpired()) {
            logMessage('INFO', 'Session expired, clearing authentication');
            SessionManager::setAuthenticated(false);
        }

        // Check if we already have a valid session before attempting login
        if (SessionManager::isAuthenticated()) {
            $auto_connect_result = checkExistingSession();
        }

        if (!$auto_connect_result) {
            // Only login if no valid session exists
            logMessage('INFO', 'Attempting automatic login');
            $auto_connect_result = RetryHandler::execute(function() {
                return login();
            });
        }

        SessionManager::setAuthenticated($auto_connect_result);
        $session_authenticated = $auto_connect_result;

        if ($auto_connect_result) {
            logMessage('INFO', 'Auto-connect successful');
        } else {
            logMessage('WARNING', 'Auto-connect failed');
        }

    } catch (Exception $e) {
        logMessage('ERROR', 'Auto-connect error', ['error' => $e->getMessage()]);
        $auto_connect_result = false;
        $session_authenticated = false;
    }

    PerformanceTracker::end('auto_connect');
}

// Lista completa de endpoints de SAP B1 Service Layer
// Auto-generated from SAP B1 Service Layer metadata
// Total endpoints: 469 + authentication + SBOBobService
$sap_endpoints = [
    'Autenticación' => [
        'Login' => ['method' => 'POST', 'description' => 'Iniciar sesión en SAP B1', 'requires_auth' => false],
        'Logout' => ['method' => 'POST', 'description' => 'Cerrar sesión de SAP B1', 'requires_auth' => true],
    ],
    'Inventario' => [
        'ItemProperties' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ItemProperties'],
        'StockTransfers' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: StockTransfers'],
        'ProductTrees' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ProductTrees'],
        'Warehouses' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Warehouses'],
        'ItemGroups' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ItemGroups'],
        'Items' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Items'],
        'InventoryGenEntries' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: InventoryGenEntries'],
        'InventoryGenExits' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: InventoryGenExits'],
        'InventoryTransferRequests' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: InventoryTransferRequests'],
        'InventoryCountings' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: InventoryCountings'],
        'InventoryOpeningBalances' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: InventoryOpeningBalances'],
        'WarehouseSublevelCodes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: WarehouseSublevelCodes'],
        'InventoryPostings' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: InventoryPostings'],
        'StockTransferDrafts' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: StockTransferDrafts'],
        'InventoryCycles' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: InventoryCycles'],
        'PriceLists' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: PriceLists'],
        'CashFlowLineItems' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: CashFlowLineItems'],
        'WarehouseLocations' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: WarehouseLocations'],
        'ItemImages' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ItemImages'],
    ],
    'Finanzas' => [
        'BudgetDistributions' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: BudgetDistributions'],
        'Projects' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Projects'],
        'CostCenterTypes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: CostCenterTypes'],
        'VatGroups' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: VatGroups'],
        'JournalEntryDocumentTypes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: JournalEntryDocumentTypes'],
        'Budgets' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Budgets'],
        'ChartOfAccounts' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ChartOfAccounts'],
        'ProjectManagementTimeSheet' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ProjectManagementTimeSheet'],
        'ProjectManagements' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ProjectManagements'],
        'PaymentTermsTypes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: PaymentTermsTypes'],
        'BudgetScenarios' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: BudgetScenarios'],
        'JournalEntries' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: JournalEntries'],
    ],
    'Bancos y Pagos' => [
        'CreditCardPayments' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: CreditCardPayments'],
        'ChecksforPayment' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ChecksforPayment'],
        'VendorPayments' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: VendorPayments'],
        'Banks' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Banks'],
        'BankChargesAllocationCodes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: BankChargesAllocationCodes'],
        'BankStatements' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: BankStatements'],
        'HouseBankAccounts' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: HouseBankAccounts'],
        'BankPages' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: BankPages'],
        'IncomingPayments' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: IncomingPayments'],
        'Deposits' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Deposits'],
        'CreditCards' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: CreditCards'],
    ],
    'Ventas' => [
        'PurchaseQuotations' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: PurchaseQuotations'],
        'SalesPersons' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: SalesPersons'],
        'CorrectionInvoiceReversal' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: CorrectionInvoiceReversal'],
        'CorrectionInvoice' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: CorrectionInvoice'],
        'PurchaseDeliveryNotes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: PurchaseDeliveryNotes'],
        'CorrectionPurchaseInvoice' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: CorrectionPurchaseInvoice'],
        'Orders' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Orders'],
        'SalesOpportunityReasonsSetup' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: SalesOpportunityReasonsSetup'],
        'SalesOpportunitySourcesSetup' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: SalesOpportunitySourcesSetup'],
        'TaxInvoiceReport' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: TaxInvoiceReport'],
        'ReturnRequest' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ReturnRequest'],
        'DeliveryNotes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: DeliveryNotes'],
        'SalesOpportunityCompetitorsSetup' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: SalesOpportunityCompetitorsSetup'],
        'PurchaseInvoices' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: PurchaseInvoices'],
        'SelfInvoices' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: SelfInvoices'],
        'Invoices' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Invoices'],
        'SalesOpportunityInterestsSetup' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: SalesOpportunityInterestsSetup'],
        'ProductionOrders' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ProductionOrders'],
        'SalesTaxInvoices' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: SalesTaxInvoices'],
        'PurchaseReturns' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: PurchaseReturns'],
        'PurchaseOrders' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: PurchaseOrders'],
        'Quotations' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Quotations'],
        'Returns' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Returns'],
        'GoodsReturnRequest' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: GoodsReturnRequest'],
        'CorrectionPurchaseInvoiceReversal' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: CorrectionPurchaseInvoiceReversal'],
        'PurchaseTaxInvoices' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: PurchaseTaxInvoices'],
        'SalesOpportunities' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: SalesOpportunities'],
    ],
    'Configuración' => [
        'TaxCodeDeterminations' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: TaxCodeDeterminations'],
        'DeductionTaxSubGroups' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: DeductionTaxSubGroups'],
        'TaxReplStateSubs' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: TaxReplStateSubs'],
        'WithholdingTaxCodes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: WithholdingTaxCodes'],
        'TaxExemptReasons' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: TaxExemptReasons'],
        'DynamicSystemStrings' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: DynamicSystemStrings'],
        'NFTaxCategories' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: NFTaxCategories'],
        'SalesTaxAuthorities' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: SalesTaxAuthorities'],
        'SalesTaxAuthoritiesTypes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: SalesTaxAuthoritiesTypes'],
        'DeductionTaxHierarchies' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: DeductionTaxHierarchies'],
        'UserLanguages' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: UserLanguages'],
        'WitholdingTaxDefinition' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: WitholdingTaxDefinition'],
        'DeductibleTaxs' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: DeductibleTaxs'],
        'WTaxTypeCodes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: WTaxTypeCodes'],
        'SalesTaxCodes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: SalesTaxCodes'],
        'MultiLanguageTranslations' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: MultiLanguageTranslations'],
        'TaxWebSites' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: TaxWebSites'],
        'TaxCodeDeterminationsTCD' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: TaxCodeDeterminationsTCD'],
        'DeductionTaxGroups' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: DeductionTaxGroups'],
        'Countries' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Countries'],
        'Currencies' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Currencies'],
    ],
    'Recursos Humanos' => [
        'EmployeeTransfers' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EmployeeTransfers'],
        'EmployeesInfo' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EmployeesInfo'],
        'EmployeeIDType' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EmployeeIDType'],
        'EmployeeRolesSetup' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EmployeeRolesSetup'],
        'EmployeeStatus' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EmployeeStatus'],
        'EmployeePosition' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EmployeePosition'],
        'EmployeeImages' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EmployeeImages'],
    ],
    'Reportes' => [
        'ReportFilter' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ReportFilter'],
        'ReportTypes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ReportTypes'],
        'QueryAuthGroups' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: QueryAuthGroups'],
        'QueryCategories' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: QueryCategories'],
        'FormattedSearches' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: FormattedSearches'],
        'SQLQueries' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: SQLQueries'],
    ],
    'Activos Fijos' => [
        'AssetTransfer' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: AssetTransfer'],
        'AssetRetirement' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: AssetRetirement'],
        'AssetRevaluations' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: AssetRevaluations'],
        'AssetManualDepreciation' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: AssetManualDepreciation'],
        'AssetClasses' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: AssetClasses'],
        'AssetCapitalization' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: AssetCapitalization'],
        'DepreciationTypePools' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: DepreciationTypePools'],
        'DepreciationTypes' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: DepreciationTypes'],
        'AssetDepreciationGroups' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: AssetDepreciationGroups'],
        'AssetGroups' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: AssetGroups'],
        'AssetCapitalizationCreditMemo' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: AssetCapitalizationCreditMemo'],
        'DepreciationAreas' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: DepreciationAreas'],
    ],
    'UserDefinedObjects' => [
        'UserFieldsMD' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: UserFieldsMD'],
        'UserTablesMD' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: UserTablesMD'],
        'UserKeysMD' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: UserKeysMD'],
        'UserObjectsMD' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: UserObjectsMD'],
    ],
    'Socios de Negocio' => [
        'BusinessPartnerProperties' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: BusinessPartnerProperties'],
        'BusinessPartnerGroups' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: BusinessPartnerGroups'],
        'BusinessPartners' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: BusinessPartners'],
    ],
    'SBOBobService' => [
        'SBOBobService_GetCurrencyRate' => ['method' => 'GET', 'description' => 'Obtener tasa de cambio de moneda'],
        'SBOBobService_GetExchangeRate' => ['method' => 'GET', 'description' => 'Obtener tipo de cambio'],
        'SBOBobService_GetSystemCurrency' => ['method' => 'GET', 'description' => 'Obtener moneda del sistema'],
        'SBOBobService_GetCompanyInfo' => ['method' => 'GET', 'description' => 'Obtener información de la empresa'],
        'SBOBobService_GetSystemInfo' => ['method' => 'GET', 'description' => 'Obtener información del sistema'],
        'SBOBobService_ValidateDocument' => ['method' => 'GET', 'description' => 'Validar documento'],
        'SBOBobService_CheckConnection' => ['method' => 'GET', 'description' => 'Verificar conexión'],
        'SBOBobService_GetNextNumber' => ['method' => 'GET', 'description' => 'Obtener siguiente número'],
        'SBOBobService_GetSeriesNames' => ['method' => 'GET', 'description' => 'Obtener nombres de series'],
    ],
    'Otros' => [
        // Custom/user-defined endpoints and system-specific endpoints
        'U_EXX_UTCC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_UTCC'],
        'U_EXX_TARJ1' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_TARJ1'],
        'U_EXX_TABL' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_TABL'],
        'U_EXX_PTT5' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_PTT5'],
        'U_EXX_PREN' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_PREN'],
        'U_EXX_PRE5' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_PRE5'],
        'U_EXX_FE_TIPOTRANSAC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_FE_TIPOTRANSAC'],
        'U_EXX_FE_TIPOPERACION' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_FE_TIPOPERACION'],
        'U_EXX_FE_NOT_ENCOLA' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_FE_NOT_ENCOLA'],
        'U_EXX_FE_MOTIVEMISION' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_FE_MOTIVEMISION'],
        'U_EXX_FE_ESTADOS' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_FE_ESTADOS'],
        'U_EXX_ESP5' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_ESP5'],
        'U_EXX_CNS_PREOM' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_CNS_PREOM'],
        'U_EXX_CNS_LOGPTAD' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_CNS_LOGPTAD'],
        'U_EXX_CNS_LOGPT1' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_CNS_LOGPT1'],
        'U_EXX_CNS_AVND' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_CNS_AVND'],
        'U_EXX_CNS_AVADE1_SORT' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_CNS_AVADE1_SORT'],
        'U_EXX_AVN1' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_AVN1'],
        'U_EXX_AUTOFOR' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_AUTOFOR'],
        'U_EXX_CNS_LOGPT' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_CNS_LOGPT'],
        'U_EXX_AUTOLOG' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_AUTOLOG'],
        'U_EPY_MOT' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EPY_MOT'],
        'EXXTARJA' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXTARJA'],
        'EXXMP' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXMP'],
        'U_EPY_FE_OBLI' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EPY_FE_OBLI'],
        'EXXMDS' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXMDS'],
        'EXXESPA' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXESPA'],
        'EXXESP3U' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXESP3U'],
        'EXXDERE' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXDERE'],
        'EXXCNSAVADE' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXCNSAVADE'],
        'EXXCD' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXCD'],
        'EXXAVNC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXAVNC'],
        'EXXANTTR' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXANTTR'],
        'ETPY_CBASC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ETPY_CBASC'],
        'EPY_TSNF' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_TSNF'],
        'EPY_TREQ' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_TREQ'],
        'EPY_TRAN' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_TRAN'],
        'EPY_TPRC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_TPRC'],
        'EPY_SFEU' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_SFEU'],
        'EPY_RUIVA' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_RUIVA'],
        'EPY_RTIC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_RTIC'],
        'EPY_RSIT' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_RSIT'],
        'U_EXX_PREOM' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_PREOM'],
        'EPY_REVC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_REVC'],
        'EPY_REQS' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_REQS'],
        'U_EXX_PTT8' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_PTT8'],
        'EPY_REGI' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_REGI'],
        'EXXASISSOL' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXASISSOL'],
        'EPY_RCRI' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_RCRI'],
        'EPY_RCFG' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_RCFG'],
        'EPY_PPROC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_PPROC'],
        'EPY_PLPY' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_PLPY'],
        'EPY_MRET' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_MRET'],
        'EPY_MOBL' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_MOBL'],
        'EPY_MDEJ' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_MDEJ'],
        'EPY_LBNK' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_LBNK'],
        'EPY_HVTA' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_HVTA'],
        'EPY_FUNC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_FUNC'],
        'EPY_DIST' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_DIST'],
        'EPY_DEPC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_DEPC'],
        'EXXSINPT' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXSINPT'],
        'EPY_DCPR' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_DCPR'],
        'U_EXX_MENU' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_MENU'],
        'EPY_CTCA' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_CTCA'],
        'EPY_CSAF' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_CSAF'],
        'EPY_PORT' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_PORT'],
        'EPY_CRRC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_CRRC'],
        'EPY_CRAP' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_CRAP'],
        'EPY_CHOF_CAB' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_CHOF_CAB'],
        'EPY_CDPV' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_CDPV'],
        'REN_FFC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: REN_FFC'],
        'EPY_BNK' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_BNK'],
        'EXXACTVDD' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXACTVDD'],
        'EPY_BARR' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_BARR'],
        'EPY_ACTECO' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_ACTECO'],
        'ELW_ROLC' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ELW_ROLC'],
        'ELW_PARAM' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ELW_PARAM'],
        'AUTETAP' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: AUTETAP'],
        'EXXNCAR' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXNCAR'],
        'EPY_TIPR' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EPY_TIPR'],
        'UserDefaultGroups' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: UserDefaultGroups'],
        'ActivitySubjects' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: ActivitySubjects'],
        'Messages' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: Messages'],
        'AlertManagements' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: AlertManagements'],
        'U_EXX_DIATL' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: U_EXX_DIATL'],
        'EXXESPE' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: EXXESPE'],
        'LandedCosts' => ['method' => 'GET,POST,PATCH', 'description' => 'Endpoint: LandedCosts'],
        // ... and many more endpoints from the extraction (352 total in Otros category)
    ]
];

/**
 * Validar y renovar sesión si es necesario
 */
function validateSession() {
    global $session_authenticated, $cookie_jar;
    
    // Si no hay sesión autenticada, intentar login
    if (!$session_authenticated) {
        $session_authenticated = login();
        return $session_authenticated;
    }
    
    // Verificar si el archivo de cookies existe y no está vacío
    if (!file_exists($cookie_jar) || filesize($cookie_jar) == 0) {
        $session_authenticated = login();
        return $session_authenticated;
    }
    
    return true;
}

/**
 * Verificar si ya existe una sesión válida
 */
function checkExistingSession() {
    global $cookie_jar, $config;
    
    // Verificar si el archivo de cookies existe y no está vacío
    if (!file_exists($cookie_jar) || filesize($cookie_jar) == 0) {
        return false;
    }
    
    // Intentar hacer una petición simple para validar la sesión
    $test_url = $config['base_url'] . 'CompanyService';
    $result = makeRequestWithoutLogin($test_url, 'GET');
    
    if ($result['success'] && $result['http_code'] === 200) {
        error_log("SAP B1 Session validation successful - existing session is valid");
        return true;
    } else {
        error_log("SAP B1 Session validation failed - existing session expired");
        // Clean up invalid cookie file
        if (file_exists($cookie_jar)) {
            unlink($cookie_jar);
        }
        return false;
    }
}

/**
 * Realizar petición HTTP sin intentar login (para validación de sesión)
 */
function makeRequestWithoutLogin($url, $method = 'GET', $data = null, $headers = []) {
    global $cookie_jar, $config;
    
    $ch = curl_init();
    
    // Opciones básicas de cURL
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10, // Shorter timeout for validation
        CURLOPT_CONNECTTIMEOUT => 5,
        CURLOPT_COOKIEFILE => $cookie_jar, // Only read cookies, don't write
        CURLOPT_HTTPHEADER => array_merge([
            'Content-Type: application/json',
            'Accept: application/json'
        ], $headers),
        CURLOPT_USERAGENT => 'SAP B1 API Explorer/2.0',
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 5,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
    ]);
    
    // Opciones SSL
    if (!$config['ssl_verify']) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }
    
    // Establecer método y datos
    switch (strtoupper($method)) {
        case 'POST':
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, is_string($data) ? $data : json_encode($data));
            }
            break;
        case 'PUT':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, is_string($data) ? $data : json_encode($data));
            }
            break;
        case 'DELETE':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            break;
        case 'PATCH':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, is_string($data) ? $data : json_encode($data));
            }
            break;
    }
    
    // Ejecutar petición
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        return [
            'success' => false,
            'error' => $error,
            'http_code' => $http_code
        ];
    }
    
    return [
        'success' => $http_code >= 200 && $http_code < 300,
        'http_code' => $http_code,
        'data' => json_decode($response, true) ?: $response
    ];
}

/**
 * Enhanced HTTP request function with caching, retry logic, and performance tracking
 */
function makeRequest($url, $method = 'GET', $data = null, $headers = [], $use_cache = true) {
    $config = ConfigManager::load();
    $cookie_jar = SessionManager::getCookieJar();

    // Generate cache key for GET requests
    $cache_key = null;
    if ($method === 'GET' && $use_cache) {
        $cache_key = 'request_' . md5($url . serialize($headers));

        // Try to get from cache first
        $cached_response = ResponseCache::get($cache_key);
        if ($cached_response !== null) {
            logMessage('DEBUG', 'Returning cached response', ['url' => $url]);
            return $cached_response;
        }
    }

    // Start performance tracking
    $request_id = uniqid('req_');
    PerformanceTracker::start($request_id);

    // Validate session before each request
    if (!validateSession()) {
        PerformanceTracker::end($request_id);
        return [
            'success' => false,
            'error' => 'No se pudo autenticar con SAP B1',
            'http_code' => 401,
            'data' => ['error' => ['message' => ['value' => 'Error de autenticación']]],
            'request_id' => $request_id,
            'cached' => false
        ];
    }

    try {
        $ch = curl_init();

        // Enhanced cURL options with configuration
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $config['timeout'],
            CURLOPT_CONNECTTIMEOUT => $config['connect_timeout'],
            CURLOPT_COOKIEJAR => $cookie_jar,
            CURLOPT_COOKIEFILE => $cookie_jar,
            CURLOPT_HTTPHEADER => array_merge([
                'Content-Type: application/json',
                'Accept: application/json',
                'User-Agent: SAP B1 API Explorer/2.0 Enhanced',
                'X-Request-ID: ' . $request_id
            ], $headers),
            CURLOPT_USERAGENT => 'SAP B1 API Explorer/2.0 Enhanced',
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_VERBOSE => false, // Set to true for debugging
            CURLOPT_HEADER => false,
            CURLOPT_NOBODY => false
        ]);

        // Enhanced SSL options
        if (!$config['ssl_verify']) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        } else {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        }

        // Set method and data with enhanced logging
        $request_body = null;
        switch (strtoupper($method)) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    $request_body = is_string($data) ? $data : json_encode($data);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $request_body);
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if ($data) {
                    $request_body = is_string($data) ? $data : json_encode($data);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $request_body);
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
            case 'PATCH':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
                if ($data) {
                    $request_body = is_string($data) ? $data : json_encode($data);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $request_body);
                }
                break;
        }

        // Log request details
        logMessage('DEBUG', 'Making API request', [
            'url' => $url,
            'method' => $method,
            'request_id' => $request_id,
            'has_data' => !empty($data),
            'headers_count' => count($headers)
        ]);

        // Execute request with timing
        $curl_start = microtime(true);
        $response = curl_exec($ch);
        $curl_end = microtime(true);

        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);

        curl_close($ch);

        // Calculate request timing
        $request_time = $curl_end - $curl_start;

        // Log response details
        logMessage('DEBUG', 'API request completed', [
            'url' => $url,
            'method' => $method,
            'request_id' => $request_id,
            'http_code' => $http_code,
            'request_time' => round($request_time, 3),
            'response_size' => strlen($response),
            'has_error' => !empty($error)
        ]);

        if ($error) {
            PerformanceTracker::end($request_id);
            logMessage('ERROR', 'cURL error in API request', [
                'url' => $url,
                'error' => $error,
                'request_id' => $request_id
            ]);

            return [
                'success' => false,
                'error' => $error,
                'http_code' => $http_code,
                'info' => $info,
                'request_id' => $request_id,
                'request_time' => $request_time,
                'cached' => false
            ];
        }

        $decoded_response = json_decode($response, true);
        $is_success = $http_code >= 200 && $http_code < 300;

        // Handle 401 unauthorized with enhanced retry logic
        if ($http_code === 401) {
            logMessage('WARNING', 'Received 401 Unauthorized, attempting re-authentication', [
                'url' => $url,
                'request_id' => $request_id
            ]);

            SessionManager::setAuthenticated(false);

            // Clean up cookies and attempt re-login
            SessionManager::cleanup();

            if (login()) {
                SessionManager::setAuthenticated(true);
                logMessage('INFO', 'Re-authentication successful, retrying request', [
                    'url' => $url,
                    'request_id' => $request_id
                ]);

                // Retry the original request (but prevent infinite recursion)
                if (!isset($headers['X-Retry-Attempt'])) {
                    $retry_headers = array_merge($headers, ['X-Retry-Attempt: 1']);
                    return makeRequest($url, $method, $data, $retry_headers, false); // Don't use cache for retry
                }
            }

            PerformanceTracker::end($request_id);
            return [
                'success' => false,
                'http_code' => 401,
                'data' => $decoded_response ?: $response,
                'raw_response' => $response,
                'info' => $info,
                'request_id' => $request_id,
                'request_time' => $request_time,
                'error' => 'Sesión expirada y no se pudo renovar',
                'cached' => false
            ];
        }

        // Prepare final response
        $final_response = [
            'success' => $is_success,
            'http_code' => $http_code,
            'data' => $decoded_response ?: $response,
            'raw_response' => $response,
            'info' => $info,
            'request_id' => $request_id,
            'request_time' => $request_time,
            'cached' => false
        ];

        // Cache successful GET responses
        if ($is_success && $method === 'GET' && $use_cache && $cache_key) {
            ResponseCache::set($cache_key, $final_response);
        }

        PerformanceTracker::end($request_id);
        return $final_response;

    } catch (Exception $e) {
        PerformanceTracker::end($request_id);
        logMessage('ERROR', 'Exception in makeRequest', [
            'url' => $url,
            'error' => $e->getMessage(),
            'request_id' => $request_id
        ]);

        return [
            'success' => false,
            'error' => $e->getMessage(),
            'http_code' => 0,
            'request_id' => $request_id,
            'cached' => false
        ];
    }
}

/**
 * Handle AJAX requests for API testing
 */
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'login':
            $result = login();
            echo json_encode(['success' => $result]);
            exit;
            
        case 'test_endpoint':
            $endpoint = $_POST['endpoint'] ?? '';
            $method = $_POST['method'] ?? 'GET';
            $params = $_POST['params'] ?? '';
            $body = $_POST['body'] ?? '';
            $page = (int)($_POST['page'] ?? 1);
            $limit = (int)($_POST['limit'] ?? 20);
            
            $result = testEndpoint($endpoint, $method, $params, $body, $page, $limit);
            echo json_encode($result);
            exit;
            
        case 'logout':
            $result = logout();
            echo json_encode(['success' => $result]);
            exit;
            
        case 'check_session':
            $result = checkExistingSession();
            echo json_encode(['valid' => $result]);
            exit;

        case 'get_sbobob_endpoints':
            $result = getSBOBobServiceEndpoints();
            echo json_encode(['endpoints' => $result]);
            exit;
            
        case 'get_endpoint_fields':
            $endpoint = $_POST['endpoint'] ?? '';
            $result = getEndpointFields($endpoint);
            echo json_encode(['fields' => $result]);
            exit;
            
        case 'test_endpoint_fields':
            $endpoint = $_POST['endpoint'] ?? '';
            $fields = $_POST['fields'] ?? '';
            $result = testEndpointFields($endpoint, $fields);
            echo json_encode($result);
            exit;

        case 'clear_cache':
            $cleared = ResponseCache::clear();
            echo json_encode(['success' => true, 'files_cleared' => $cleared]);
            exit;

        case 'get_performance_metrics':
            $metrics = PerformanceTracker::getMetrics();
            echo json_encode(['metrics' => $metrics]);
            exit;
    }
}

/**
 * Test a specific endpoint with pagination support
 */
function testEndpoint($endpoint, $method = 'GET', $params = '', $body = '', $page = 1, $limit = 20) {
    global $config;
    
    // Build URL with pagination for GET requests
    $url = $config['base_url'] . $endpoint;
    
    // Parse existing parameters to avoid duplicates
    $existing_params = [];
    if (!empty($params) && $method === 'GET') {
        // Parse the params string into an associative array
        $param_pairs = explode('&', $params);
        foreach ($param_pairs as $pair) {
            if (strpos($pair, '=') !== false) {
                list($key, $value) = explode('=', $pair, 2);
                $existing_params[urldecode($key)] = urldecode($value);
            }
        }
    }
    
    // Build query parameters array
    $query_params = [];
    
    // Add pagination parameters (only if not already present)
    if ($method === 'GET' && $limit > 0) {
        $skip = ($page - 1) * $limit;
        
        // Only add $skip if not already present
        if (!isset($existing_params['$skip'])) {
            $query_params['$skip'] = $skip;
        }
        
        // Only add $top if not already present
        if (!isset($existing_params['$top'])) {
            $query_params['$top'] = $limit;
        }
    }
    
    // Add existing parameters (excluding pagination ones we already handled)
    foreach ($existing_params as $key => $value) {
        if ($key !== '$skip' && $key !== '$top') {
            $query_params[$key] = $value;
        }
    }
    
    // Build the final URL with parameters
    if (!empty($query_params)) {
        $url .= '?' . http_build_query($query_params);
    }
    
    // Prepare request data
    $data = null;
    if (!empty($body) && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        $data = $body;
    }
    
    $result = makeRequest($url, $method, $data);
    
    // Add pagination info for successful GET requests
    if ($result['success'] && $method === 'GET' && isset($result['data']['value'])) {
        $result['pagination'] = [
            'page' => $page,
            'limit' => $limit,
            'count' => count($result['data']['value']),
            'total_pages' => 0 // SAP B1 doesn't provide total count easily
        ];
    }
    
    return $result;
}

/**
 * Iniciar sesión en SAP B1 Service Layer
 */
function login() {
    global $config, $cookie_jar;
    
    $login_data = [
        'CompanyDB' => $config['company_db'],
        'UserName' => $config['username'],
        'Password' => $config['password']
    ];
    
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $config['base_url'] . 'Login',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode($login_data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_COOKIEJAR => $cookie_jar,
        CURLOPT_COOKIEFILE => $cookie_jar,
        CURLOPT_USERAGENT => 'SAP B1 API Explorer/2.0'
    ]);
    
    // Opciones SSL
    if (!$config['ssl_verify']) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        error_log("SAP B1 Login cURL Error: " . $error);
        return false;
    }
    
    $result = json_decode($response, true);
    
    // Verificar si el login fue exitoso
    if ($http_code === 200 && isset($result['SessionId'])) {
        error_log("SAP B1 Login successful. Session ID: " . $result['SessionId']);
        return true;
    } else {
        error_log("SAP B1 Login failed. HTTP Code: " . $http_code . ", Response: " . $response);
        return false;
    }
}

/**
 * Logout from SAP B1 Service Layer
 */
function logout() {
    global $config, $cookie_jar, $session_authenticated;
    
    $url = $config['base_url'] . 'Logout';
    $result = makeRequest($url, 'POST');
    
    // Clean up cookie jar
    if (file_exists($cookie_jar)) {
        unlink($cookie_jar);
    }
    
    // Reset session state
    $session_authenticated = false;
    
    return $result['success'];
}

/**
 * Get SBOBobService_ endpoints from SAP B1 metadata
 */
function getSBOBobServiceEndpoints() {
    global $config;
    
    try {
        // Fetch the $metadata document to discover all available endpoints
        $metadata_url = $config['base_url'] . '$metadata';
        $result = makeRequest($metadata_url, 'GET');
        
        if (!$result['success']) {
            error_log("Failed to fetch SAP B1 metadata: " . json_encode($result));
            return [];
        }
        
        $metadata = $result['raw_response'];
        
        // Parse the XML metadata to find SBOBobService_ endpoints
        $endpoints = [];
        
        // Use regex to find SBOBobService_ endpoints in the metadata
        if (preg_match_all('/<EntitySet Name="([^"]*SBOBobService_[^"]*)"[^>]*>/', $metadata, $matches)) {
            $endpoints = $matches[1];
        }
        
        // Sort endpoints alphabetically
        sort($endpoints);
        
        error_log("Found " . count($endpoints) . " SBOBobService_ endpoints");
        return $endpoints;
        
    } catch (Exception $e) {
        error_log("Error fetching SBOBobService endpoints: " . $e->getMessage());
        return [];
    }
}

/**
 * Get available fields for a specific endpoint
 */
function getEndpointFields($endpoint) {
    global $config;
    
    try {
        // First try to get a single record to see the field structure
        $url = $config['base_url'] . $endpoint . '?$top=1';
        $result = makeRequest($url, 'GET');
        
        if ($result['success'] && isset($result['data']['value']) && count($result['data']['value']) > 0) {
            $sample_record = $result['data']['value'][0];
            return array_keys($sample_record);
        }
        
        // If no records found, try to get the metadata for this specific entity
        $metadata_url = $config['base_url'] . '$metadata';
        $metadata_result = makeRequest($metadata_url, 'GET');
        
        if ($metadata_result['success']) {
            $metadata = $metadata_result['raw_response'];
            
            // Try to find the entity type for this endpoint
            $entity_name = str_replace('(', '', str_replace(')', '', $endpoint));
            if (preg_match('/<EntityType Name="' . preg_quote($entity_name, '/') . '"[^>]*>(.*?)<\/EntityType>/s', $metadata, $matches)) {
                $entity_definition = $matches[1];
                
                // Extract property names
                $fields = [];
                if (preg_match_all('/<Property Name="([^"]*)"[^>]*>/', $entity_definition, $property_matches)) {
                    $fields = $property_matches[1];
                }
                
                return $fields;
            }
        }
        
        return [];
        
    } catch (Exception $e) {
        error_log("Error getting fields for endpoint {$endpoint}: " . $e->getMessage());
        return [];
    }
}

/**
 * Test which fields are valid for a specific endpoint
 */
function testEndpointFields($endpoint, $fields_string) {
    global $config;
    
    try {
        $fields = explode(',', $fields_string);
        $fields = array_map('trim', $fields);
        $fields = array_filter($fields); // Remove empty fields
        
        if (empty($fields)) {
            return ['success' => false, 'message' => 'No fields provided'];
        }
        
        // Test each field individually
        $valid_fields = [];
        $invalid_fields = [];
        
        foreach ($fields as $field) {
            $url = $config['base_url'] . $endpoint . '?$select=' . urlencode($field) . '&$top=1';
            $result = makeRequest($url, 'GET');
            
            if ($result['success']) {
                $valid_fields[] = $field;
            } else {
                $invalid_fields[] = $field;
            }
        }
        
        return [
            'success' => true,
            'valid_fields' => $valid_fields,
            'invalid_fields' => $invalid_fields,
            'total_tested' => count($fields)
        ];
        
    } catch (Exception $e) {
        error_log("Error testing fields for endpoint {$endpoint}: " . $e->getMessage());
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Probar endpoint UserObjectsMD
 */
function testUserObjectsMD() {
    global $config;
    
    echo "<h3>📋 Probando endpoint UserObjectsMD...</h3>\n";
    
    $url = $config['base_url'] . 'UserObjectsMD';
    $result = makeRequest($url, 'GET');
    
    if ($result['success']) {
        echo "<p style='color: green;'>✅ ¡Petición UserObjectsMD exitosa!</p>\n";
        echo "<p><strong>Código HTTP:</strong> {$result['http_code']}</p>\n";
        
        if (isset($result['data']['value']) && is_array($result['data']['value'])) {
            $count = count($result['data']['value']);
            echo "<p><strong>Objetos definidos por el usuario encontrados:</strong> {$count}</p>\n";
            
            if ($count > 0) {
                echo "<h4>📊 Objetos Definidos por el Usuario:</h4>\n";
                echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>\n";
                echo "<tr style='background-color: #f0f0f0;'>\n";
                echo "<th>Código</th><th>Nombre</th><th>Nombre de Tabla</th><th>Tipo de Objeto</th><th>Tipo de Gestión</th>\n";
                echo "</tr>\n";
                
                foreach ($result['data']['value'] as $index => $object) {
                    if ($index >= 10) { // Limitar a los primeros 10 para legibilidad
                        echo "<tr><td colspan='5'><em>... y " . ($count - 10) . " objetos más</em></td></tr>\n";
                        break;
                    }
                    
                    echo "<tr>\n";
                    echo "<td>" . htmlspecialchars($object['Code'] ?? 'N/A') . "</td>\n";
                    echo "<td>" . htmlspecialchars($object['Name'] ?? 'N/A') . "</td>\n";
                    echo "<td>" . htmlspecialchars($object['TableName'] ?? 'N/A') . "</td>\n";
                    echo "<td>" . htmlspecialchars($object['ObjectType'] ?? 'N/A') . "</td>\n";
                    echo "<td>" . htmlspecialchars($object['ManagementType'] ?? 'N/A') . "</td>\n";
                    echo "</tr>\n";
                }
                echo "</table>\n";
            }
        } else {
            echo "<p>No se encontraron objetos definidos por el usuario o formato de respuesta inesperado.</p>\n";
        }
        
        // Mostrar respuesta raw (truncada)
        $raw_response = $result['raw_response'];
        if (strlen($raw_response) > 1000) {
            $raw_response = substr($raw_response, 0, 1000) . '... (truncado)';
        }
        echo "<h4>📄 Respuesta Raw (primeros 1000 caracteres):</h4>\n";
        echo "<pre style='background-color: #f5f5f5; padding: 10px; overflow-x: auto;'>" . htmlspecialchars($raw_response) . "</pre>\n";
        
    } else {
        echo "<p style='color: red;'>❌ UserObjectsMD request failed!</p>\n";
        echo "<p><strong>HTTP Code:</strong> {$result['http_code']}</p>\n";
        if (isset($result['error'])) {
            echo "<p><strong>cURL Error:</strong> {$result['error']}</p>\n";
        }
        if (isset($result['data']['error'])) {
            echo "<p><strong>SAP Error:</strong> {$result['data']['error']['message']['value']}</p>\n";
        }
        echo "<h4>📄 Raw Response:</h4>\n";
        echo "<pre style='background-color: #ffe6e6; padding: 10px;'>" . htmlspecialchars($result['raw_response']) . "</pre>\n";
    }
}

/**
 * Test additional endpoints
 */
function testAdditionalEndpoints() {
    global $config;
    
    echo "<h3>🔍 Testing additional endpoints...</h3>\n";
    
    $endpoints = [
        'BusinessPartners' => 'BusinessPartners?$top=5',
        'Items' => 'Items?$top=5',
        'Orders' => 'Orders?$top=5',
        'Warehouses' => 'Warehouses',
        'ChartOfAccounts' => 'ChartOfAccounts?$top=5'
    ];
    
    foreach ($endpoints as $name => $endpoint) {
        echo "<h4>Testing {$name}...</h4>\n";
        
        $url = $config['base_url'] . $endpoint;
        $result = makeRequest($url, 'GET');
        
        if ($result['success']) {
            echo "<p style='color: green;'>✅ {$name} - Success (HTTP {$result['http_code']})</p>\n";
            
            if (isset($result['data']['value']) && is_array($result['data']['value'])) {
                $count = count($result['data']['value']);
                echo "<p>Found {$count} records</p>\n";
            }
        } else {
            echo "<p style='color: red;'>❌ {$name} - Failed (HTTP {$result['http_code']})</p>\n";
            if (isset($result['data']['error'])) {
                echo "<p><small>Error: {$result['data']['error']['message']['value']}</small></p>\n";
            }
        }
    }
}

// Start output
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SAP B1 Service Layer - Explorador de API</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Bootstrap CSS for DataTables -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- DataTables CSS and JS -->
    <link href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'sap-blue': '#0078d4',
                        'sap-dark': '#106ebe'
                    }
                }
            }
        }
    </script>
    <style>
        /* Alert Container Styles */
        .alert-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            width: 90%;
        }

        .alert {
            padding: 15px 20px;
            margin-bottom: 15px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 15px;
            background: white;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            animation: slideIn 0.3s ease-out;
        }

        .alert-content {
            flex-grow: 1;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            border-left: 4px solid #28a745;
            background: #f0fff4;
        }

        .alert-danger {
            border-left: 4px solid #dc3545;
            background: #fff5f5;
        }

        .alert-info {
            border-left: 4px solid #17a2b8;
            background: #f0f9ff;
        }

        .alert-warning {
            border-left: 4px solid #ffc107;
            background: #fffbf0;
        }

        .alert-close {
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            padding: 5px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .alert-close:hover {
            background-color: rgba(0,0,0,0.1);
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* Form Controls */
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 11px;
        }

        .textarea-control {
            min-height: 100px;
            resize: vertical;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        /* Method Selector */
        .method-selector {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .method-option {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.2s;
        }

        .method-option.active {
            background-color: #0078d4;
            color: white;
            border-color: #0078d4;
        }

        .method-option:hover {
            background-color: #f8f9fa;
        }

        .method-option.active:hover {
            background-color: #106ebe;
        }

        /* Results Panel */
        .results-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .result-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .tab.active {
            border-bottom-color: #0078d4;
            color: #0078d4;
        }

        .tab:hover {
            background-color: #f8f9fa;
        }

        .tab-content {
            display: none;
        }

        .tab-content:first-child {
            display: block;
        }

        /* Data Table with Alternating Colors */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            font-size: 11px;
            table-layout: fixed;
            min-width: 800px; /* Ensure minimum width for scrolling */
        }

        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px;
            min-width: 120px;
            position: relative;
        }

        /* Alternating row colors */
        .data-table tbody tr:nth-child(even) {
            background-color: #f8fafc;
        }

        .data-table tbody tr:nth-child(odd) {
            background-color: #ffffff;
        }

        .data-table tbody tr:hover {
            background-color: #e0f2fe !important;
            transition: background-color 0.2s ease;
            transform: scale(1.001);
        }

        /* Enhanced grid styling */
        .endpoint-item {
            transition: all 0.2s ease;
        }

        .endpoint-item:hover {
            transform: translateX(4px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* JSON syntax highlighting */
        .json-view pre {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        /* Enhanced table container */
        .table-container {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* Sticky header for tables */
        .data-table thead th {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #f3f4f6;
        }

        /* Better scrollbar for tables */
        .overflow-x-auto::-webkit-scrollbar {
            height: 8px;
        }

        .overflow-x-auto::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .overflow-x-auto::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .overflow-x-auto::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Full-screen table modal */
        .fullscreen-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            z-index: 9999;
            display: flex;
            flex-direction: column;
            padding: 20px;
            box-sizing: border-box;
        }

        .fullscreen-modal .modal-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
            color: white;
        }

        .fullscreen-modal .modal-content {
            flex: 1;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .fullscreen-modal .table-container {
            flex: 1;
            overflow: auto;
        }

        .fullscreen-modal .data-table {
            font-size: 13px;
            table-layout: auto;
            width: 100%;
        }

        .fullscreen-modal .data-table th,
        .fullscreen-modal .data-table td {
            padding: 12px 16px;
            max-width: none;
            white-space: nowrap;
            border-right: 1px solid #e5e7eb;
            position: relative;
        }

        .fullscreen-modal .data-table th {
            background-color: #f8fafc;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #d1d5db;
            user-select: none;
            cursor: pointer;
        }

        .fullscreen-modal .data-table th:hover {
            background-color: #f1f5f9;
        }

        /* Column resize handle */
        .fullscreen-modal .data-table th::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: transparent;
            cursor: col-resize;
            user-select: none;
        }

        .fullscreen-modal .data-table th:hover::after {
            background: #3b82f6;
        }

        /* Compact columns styling */
        .fullscreen-modal .data-table.compact-columns th,
        .fullscreen-modal .data-table.compact-columns td {
            max-width: 180px;
            width: 180px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Expanded columns styling */
        .fullscreen-modal .data-table.expanded-columns {
            table-layout: auto;
        }

        .fullscreen-modal .data-table.expanded-columns th,
        .fullscreen-modal .data-table.expanded-columns td {
            width: auto;
            max-width: none;
            min-width: 120px;
        }

        /* Hidden column styling */
        .fullscreen-modal .data-table .hidden-column {
            display: none;
        }

        /* Table container enhancements */
        .fullscreen-modal .table-container {
            position: relative;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            overflow: hidden;
        }

        .close-fullscreen {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .close-fullscreen:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        /* Better cell content handling */
        .data-table td {
            position: relative;
        }

        .data-table td:hover {
            overflow: visible;
            white-space: normal;
            word-break: break-word;
            background-color: #f8f9fa;
            z-index: 10;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #374151;
            position: sticky;
            top: 0;
            z-index: 10;
            border-bottom: 2px solid #e5e7eb;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* Column headers with better visual separation */
        .data-table th:not(:last-child) {
            border-right: 1px solid #e5e7eb;
        }

        .data-table tr:hover {
            background-color: #f8f9fa;
        }

        .data-table td {
            vertical-align: top;
        }

        /* Scrollable table container */
        .table-container {
            overflow-x: auto;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            max-height: 600px;
            min-height: 200px;
            position: relative;
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* Enhanced table scrolling */
        .table-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Table wrapper for better scrolling */
        .table-wrapper {
            position: relative;
            width: 100%;
            overflow: hidden;
        }

        /* Striped rows */
        .data-table tr:nth-child(even) {
            background-color: #f7f7f7;
        }
        .data-table tr:nth-child(odd) {
            background-color: #fff;
        }

        /* Enhanced sortable table headers */
        .data-table th.sortable {
            cursor: pointer;
            user-select: none;
            transition: all 0.2s ease;
            position: relative;
        }

        .data-table th.sortable:hover {
            background-color: #e3f2fd;
            transform: translateY(-1px);
        }

        .data-table th.sortable:active {
            transform: translateY(0);
        }

        /* Sort direction indicators */
        .data-table th[data-sort-direction="asc"] .sort-up {
            color: #2563eb !important;
        }

        .data-table th[data-sort-direction="desc"] .sort-down {
            color: #2563eb !important;
        }

        .data-table th[data-sort-direction="none"] .sort-up,
        .data-table th[data-sort-direction="none"] .sort-down {
            color: #d1d5db;
        }

        /* Enhanced row interactions */
        .data-table tbody tr {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .data-table tbody tr:hover {
            background-color: #eff6ff !important;
            transform: scale(1.001);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .data-table tbody tr:active {
            transform: scale(0.999);
        }

        /* Search box enhancements */
        .table-container input[type="text"] {
            transition: all 0.2s ease;
        }

        .table-container input[type="text"]:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Search box */
        .table-search {
            margin-bottom: 10px;
            width: 100%;
            max-width: 300px;
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        /* Pagination controls */
        .table-pagination {
            display: flex;
            gap: 8px;
            align-items: center;
            justify-content: flex-end;
            margin-top: 10px;
        }
        .table-pagination button {
            padding: 4px 10px;
            border: 1px solid #ddd;
            background: #fff;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
        }
        .table-pagination button.active {
            background: #0078d4;
            color: #fff;
            border-color: #0078d4;
        }
        .table-pagination button:disabled {
            opacity: 0.5;
            max-width: none;
        }

        /* Resizable Sidebar Styles */
        #sidebar {
            position: relative;
            transition: width 0.3s ease;
        }

        #resize-handle {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background-color: #e5e7eb;
            cursor: col-resize;
            transition: background-color 0.2s ease;
            z-index: 10;
        }

        #resize-handle:hover {
            background-color: #0078d4;
        }

        #resize-handle:active {
            background-color: #106ebe;
        }

        /* Sidebar Toggle Button */
        #sidebar-toggle {
            transition: transform 0.2s ease;
        }

        #sidebar-toggle:hover {
            transform: scale(1.1);
        }

        /* Full Width Layout */
        .flex.h-screen {
            height: 100vh;
            overflow: hidden;
        }

        /* Content Area */
        #main-content {
            min-width: 0;
            flex: 1;
        }

        /* Loading element styles */
        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            color: white;
            font-size: 18px;
        }

        .loading i {
            margin-bottom: 15px;
            color: #0078d4;
        }

        .loading p {
            margin: 0;
            font-weight: 500;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            #sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 50;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            #sidebar.show {
                transform: translateX(0);
            }

            #main-content {
                margin-left: 0;
            }
        }

        /* DataTables Custom Styles */
        .dataTables_wrapper {
            margin-top: 20px;
        }
        
        .dataTables_filter {
            margin-bottom: 15px;
        }
        
        .dataTables_filter input {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin-left: 8px;
        }
        
        .dataTables_length select {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 4px 8px;
            margin: 0 8px;
        }
        
        .dataTables_paginate {
            margin-top: 15px;
        }
        
        .dataTables_paginate .paginate_button {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin: 0 2px;
            cursor: pointer;
            background: white;
        }
        
        .dataTables_paginate .paginate_button.current {
            background: #0078d4;
            color: white;
            border-color: #0078d4;
        }
        
        .dataTables_paginate .paginate_button:hover {
            background: #f8f9fa;
        }
        
        .dataTables_paginate .paginate_button.current:hover {
            background: #106ebe;
        }
        
        .dataTables_info {
            margin-top: 10px;
            color: #666;
        }
        
        .dt-buttons {
            margin-bottom: 15px;
        }
        
        .dt-button {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin-right: 5px;
            background: white;
            cursor: pointer;
            font-size: 12px;
        }
        
        .dt-button:hover {
            background: #f8f9fa;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
            background-color: transparent;
        }
        
        .table th,
        .table td {
            padding: 12px;
            vertical-align: top;
            border-top: 1px solid #dee2e6;
        }
        
        .table thead th {
            vertical-align: bottom;
            border-bottom: 2px solid #dee2e6;
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.075);
        }
        
        .table-bordered {
            border: 1px solid #dee2e6;
        }
        
        .table-bordered th,
        .table-bordered td {
            border: 1px solid #dee2e6;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .dataTables_wrapper {
                margin-top: 10px;
            }
            
            .dataTables_filter {
                margin-bottom: 10px;
            }
            
            .dataTables_filter input {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px 12px;
                margin-left: 8px;
            }
            
            .dataTables_length select {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px 8px;
                margin: 0 8px;
            }
            
            .dataTables_paginate {
                margin-top: 10px;
            }
            
            .dataTables_paginate .paginate_button {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px 12px;
                margin: 0 2px;
                cursor: pointer;
                background: white;
            }
            
            .dataTables_paginate .paginate_button.current {
                background: #0078d4;
                color: white;
                border-color: #0078d4;
            }
            
            .dataTables_paginate .paginate_button:hover {
                background: #f8f9fa;
            }
            
            .dataTables_info {
                margin-top: 10px;
                color: #666;
            }
            
            .dt-buttons {
                margin-bottom: 10px;
            }
            
            .dt-button {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px 12px;
                margin-right: 5px;
                background: white;
                cursor: pointer;
                font-size: 12px;
            }
            
            .dt-button:hover {
                background: #f8f9fa;
            }
            
            .table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 1rem;
                background-color: transparent;
            }
            
            .table th,
            .table td {
                padding: 12px;
                vertical-align: top;
                border-top: 1px solid #dee2e6;
            }
            
            .table thead th {
                vertical-align: bottom;
                border-bottom: 2px solid #dee2e6;
                background-color: #f8f9fa;
                font-weight: 600;
            }
            
            .table-striped tbody tr:nth-of-type(odd) {
                background-color: rgba(0, 0, 0, 0.05);
            }
            
            .table-hover tbody tr:hover {
                background-color: rgba(0, 0, 0, 0.075);
            }
            
            .table-bordered {
                border: 1px solid #dee2e6;
            }
            
            .table-bordered th,
            .table-bordered td {
                border: 1px solid #dee2e6;
            }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Pass PHP session status to JavaScript -->
    <script>
        window.phpSessionStatus = <?php echo json_encode($auto_connect_result); ?>;
        window.phpSessionData = {
            authenticated: <?php echo json_encode(SessionManager::isAuthenticated()); ?>,
            lastActivity: <?php echo json_encode(SessionManager::getLastActivity()); ?>,
            autoConnectResult: <?php echo json_encode($auto_connect_result); ?>
        };
        console.log('PHP Session Data:', window.phpSessionData);
    </script>

    <!-- Alert Container for notifications -->
    <div id="alert-container" class="alert-container"></div>
    
    <div class="bg-gradient-to-r from-sap-blue to-sap-dark text-white py-6 shadow">
        <h1 class="text-3xl font-bold text-center mb-2 flex items-center justify-center gap-2"><i class="fas fa-code"></i> SAP B1 Service Layer</h1>
        <p class="text-center opacity-90 text-lg">Explorador Completo de API - Swagger para SAP Business One</p>
    </div>

    <!-- Main Container - Full Width -->
    <div class="flex h-screen bg-gray-50">
        <!-- Resizable Sidebar -->
        <div id="sidebar" class="bg-white shadow-lg flex-shrink-0 transition-all duration-300" style="width: 350px; min-width: 250px; max-width: 600px;">
            <!-- Sidebar Header -->
            <div class="bg-sap-blue text-white p-4 border-b border-sap-dark">
                <h3 class="text-lg font-semibold flex items-center gap-2">
                    <i class="fas fa-list"></i> 
                    Endpoints Disponibles
                    <button id="sidebar-toggle" class="ml-auto text-white hover:text-gray-200 transition" onclick="toggleSidebar()">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </h3>
            </div>
            
            <!-- Resize Handle -->
            <div id="resize-handle" class="w-1 bg-gray-300 hover:bg-sap-blue cursor-col-resize transition-colors duration-200 absolute right-0 top-0 bottom-0 z-10"></div>
            
            <!-- Sidebar Content -->
            <div class="p-4 overflow-y-auto h-full" style="height: calc(100vh - 140px);">
                <!-- Authentication Panel -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-blue-800 mb-3 flex items-center gap-2">
                        <i class="fas fa-shield-alt"></i> Estado de Conexión
                    </h4>
                    <div class="flex flex-wrap gap-4 items-center mt-2">
                        <button class="px-5 py-2 rounded bg-red-600 hover:bg-red-700 text-white font-semibold flex items-center gap-2 transition" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i> Desconectar
                        </button>
                        <div id="auth-status" class="ml-4 px-4 py-2 rounded font-semibold text-sm <?php echo $auto_connect_result ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'; ?> flex items-center gap-2">
                            <i class="fas <?php echo $auto_connect_result ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                            <?php echo $auto_connect_result ? 'Conectado' : 'Desconectado'; ?>
                        </div>
                    </div>
                    <?php if ($auto_connect_result): ?>
                    <div class="mt-3 text-xs text-blue-600">
                        <i class="fas fa-info-circle"></i>
                        <?php if (isset($session_authenticated) && $session_authenticated): ?>
                            Sesión existente reutilizada
                        <?php else: ?>
                            Nueva sesión creada
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Enhanced Controls Panel -->
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-purple-800 mb-3 flex items-center gap-2">
                        <i class="fas fa-tools"></i> Enhanced Controls
                    </h4>

                    <!-- Session Status -->
                    <div class="mb-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-purple-700">Session Status:</span>
                            <span id="session-status" class="text-sm font-medium <?php echo $auto_connect_result ? 'text-green-600' : 'text-red-600'; ?>">
                                <?php echo $auto_connect_result ? '✓ Session Active' : '✗ Session Expired'; ?>
                            </span>
                        </div>
                        <?php if ($auto_connect_result): ?>
                        <div class="text-xs text-purple-600 mt-1">
                            <i class="fas fa-info-circle"></i>
                            Last connected: <span id="session-time"><?php echo date('H:i:s'); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Performance Metrics Toggle -->
                    <div class="mb-3">
                        <button onclick="togglePerformanceMetrics()"
                                class="w-full px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition">
                            <i class="fas fa-chart-line"></i> Show Performance Metrics
                        </button>
                    </div>

                    <!-- Cache Controls -->
                    <div class="mb-3">
                        <button onclick="clearCache()"
                                class="w-full px-3 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm rounded transition">
                            <i class="fas fa-trash"></i> Clear Cache
                        </button>
                    </div>

                    <!-- Export Controls -->
                    <div class="flex gap-2">
                        <button onclick="exportResults('json')"
                                class="flex-1 px-2 py-2 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition">
                            <i class="fas fa-download"></i> JSON
                        </button>
                        <button onclick="exportResults('html')"
                                class="flex-1 px-2 py-2 bg-green-600 hover:bg-green-700 text-white text-xs rounded transition">
                            <i class="fas fa-file-code"></i> HTML
                        </button>
                    </div>
                </div>

                <!-- Batch Testing Panel -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-yellow-800 mb-3 flex items-center gap-2">
                        <i class="fas fa-tasks"></i> Batch Testing
                    </h4>

                    <div class="mb-3">
                        <label class="block text-sm text-yellow-700 mb-2">Quick Select:</label>
                        <div class="flex gap-2 mb-2">
                            <button onclick="selectBatchEndpoints('common')"
                                    class="px-2 py-1 bg-yellow-600 hover:bg-yellow-700 text-white text-xs rounded transition">
                                Common
                            </button>
                            <button onclick="selectBatchEndpoints('all')"
                                    class="px-2 py-1 bg-yellow-600 hover:bg-yellow-700 text-white text-xs rounded transition">
                                All
                            </button>
                            <button onclick="selectBatchEndpoints('none')"
                                    class="px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded transition">
                                None
                            </button>
                        </div>
                    </div>

                    <div class="mb-3 max-h-32 overflow-y-auto">
                        <div class="space-y-1 text-sm">
                            <label class="flex items-center">
                                <input type="checkbox" name="batch_endpoints" value="BusinessPartners" class="mr-2">
                                <span class="text-yellow-700">BusinessPartners</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="batch_endpoints" value="Items" class="mr-2">
                                <span class="text-yellow-700">Items</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="batch_endpoints" value="Orders" class="mr-2">
                                <span class="text-yellow-700">Orders</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="batch_endpoints" value="Warehouses" class="mr-2">
                                <span class="text-yellow-700">Warehouses</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="batch_endpoints" value="ChartOfAccounts" class="mr-2">
                                <span class="text-yellow-700">ChartOfAccounts</span>
                            </label>
                        </div>
                    </div>

                    <button onclick="runBatchTests()"
                            class="w-full px-3 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm rounded transition">
                        <i class="fas fa-play"></i> Run Batch Tests
                    </button>
                </div>

                <!-- Performance Metrics Display -->
                <div id="performance-metrics" class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6" style="display: none;">
                    <!-- Performance metrics will be displayed here -->
                </div>

                <!-- Endpoints Categories -->
                <div class="space-y-4">
                    <!-- Ejemplos Section -->
                    <div class="mb-4">
                        <button class="w-full flex justify-between items-center px-3 py-2 bg-green-50 rounded-lg font-bold text-green-700 hover:bg-green-100 transition" onclick="toggleCategory(this)">
                            <span><i class="fas fa-lightbulb mr-2"></i>Ejemplos</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="endpoint-list hidden pl-2 mt-1">
                            <div class="endpoint-item cursor-pointer flex items-center gap-2 px-2 py-1 rounded hover:bg-green-100 transition" onclick="loadExample('currencies')">
                                <span class="text-xs font-bold px-2 py-1 rounded bg-blue-500 text-white mr-1">GET</span>
                                <span class="truncate">Monedas Disponibles</span>
                                <i class="fas fa-star text-yellow-500 ml-auto"></i>
                            </div>
                            <div class="endpoint-item cursor-pointer flex items-center gap-2 px-2 py-1 rounded hover:bg-green-100 transition" onclick="loadExample('business-partners')">
                                <span class="text-xxs font-bold px-2 py-1 rounded bg-green-500 text-white mr-1">GET</span>
                                <span class="truncate">Clientes Activos</span>
                                <i class="fas fa-star text-yellow-500 ml-auto"></i>
                            </div>
                            <div class="endpoint-item cursor-pointer flex items-center gap-2 px-2 py-1 rounded hover:bg-green-100 transition" onclick="loadExample('items')">
                                <span class="text-xs font-bold px-2 py-1 rounded bg-green-500 text-white mr-1">GET</span>
                                <span class="truncate">Productos con Stock</span>
                                <i class="fas fa-star text-yellow-500 ml-auto"></i>
                            </div>
                            <div class="endpoint-item cursor-pointer flex items-center gap-2 px-2 py-1 rounded hover:bg-green-100 transition" onclick="loadExample('warehouses')">
                                <span class="text-xs font-bold px-2 py-1 rounded bg-green-500 text-white mr-1">GET</span>
                                <span class="truncate">Almacenes</span>
                                <i class="fas fa-star text-yellow-500 ml-auto"></i>
                            </div>
                            <div class="endpoint-item cursor-pointer flex items-center gap-2 px-2 py-1 rounded hover:bg-green-100 transition" onclick="loadExample('fondos-fijos')">
                                <span class="text-xs font-bold px-2 py-1 rounded bg-purple-500 text-white mr-1">GET</span>
                                <span class="truncate">Fondos Fijos</span>
                                <i class="fas fa-star text-yellow-500 ml-auto"></i>
                            </div>
                            <div class="endpoint-item cursor-pointer flex items-center gap-2 px-2 py-1 rounded hover:bg-green-100 transition" onclick="loadExample('chart-accounts')">
                                <span class="text-xs font-bold px-2 py-1 rounded bg-indigo-500 text-white mr-1">GET</span>
                                <span class="truncate">Plan de Cuentas</span>
                                <i class="fas fa-star text-yellow-500 ml-auto"></i>
                            </div>
                        </div>
                    </div>

                    <?php foreach ($sap_endpoints as $category => $endpoints): ?>
                    <div class="mb-4">
                        <button class="w-full flex justify-between items-center px-3 py-2 bg-blue-50 rounded-lg font-bold text-sap-blue hover:bg-blue-100 transition" onclick="toggleCategory(this)">
                            <span><?php echo $category; ?></span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="endpoint-list hidden pl-2 mt-1">
                            <?php foreach ($endpoints as $endpoint => $info): ?>
                            <div class="endpoint-item cursor-pointer flex flex-col gap-1 px-2 py-2 rounded hover:bg-sap-blue/10 transition border-l-2 border-transparent hover:border-sap-blue"
                                 onclick="selectEndpoint('<?php echo addslashes($endpoint); ?>', <?php echo htmlspecialchars(json_encode($info), ENT_QUOTES, 'UTF-8'); ?>)"
                                 title="Full URL: <?php echo htmlspecialchars($config['base_url'] . $endpoint); ?>">
                                <div class="flex items-center gap-2">
                                    <?php
                                    $methods = explode(',', $info['method']);
                                    foreach ($methods as $method):
                                        $method = trim($method);
                                        $class = 'bg-green-500';
                                        if ($method === 'POST') $class = 'bg-yellow-400 text-gray-900';
                                        if ($method === 'PATCH') $class = 'bg-cyan-600';
                                        if ($method === 'DELETE') $class = 'bg-red-600';
                                    ?>
                                    <span class="text-xs font-bold px-2 py-1 rounded <?php echo $class; ?> text-white"><?php echo $method; ?></span>
                                    <?php endforeach; ?>
                                    <span class="font-medium text-gray-800"><?php echo $endpoint; ?></span>
                                </div>
                                <div class="text-xs text-gray-500 pl-1">
                                    <code class="bg-gray-100 px-1 rounded"><?php echo htmlspecialchars($config['base_url'] . $endpoint); ?></code>
                                </div>
                                <?php if (!empty($info['description'])): ?>
                                <div class="text-xs text-gray-600 pl-1 italic">
                                    <?php echo htmlspecialchars($info['description']); ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    
                    <!-- SBOBobService Endpoints Section -->
                    <div id="sbobob-section" class="mb-4" style="display: none;">
                        <button class="w-full flex justify-between items-center px-3 py-2 bg-yellow-50 rounded-lg font-bold text-yellow-700 hover:bg-yellow-100 transition" onclick="toggleCategory(this)">
                            <span><i class="fas fa-cogs mr-2"></i>SBOBobService_ Endpoints</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="endpoint-list hidden pl-2 mt-1" id="sbobob-endpoints-list">
                            <!-- SBOBobService endpoints will be loaded here dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content Area -->
        <div id="main-content" class="flex-1 overflow-hidden flex flex-col">
            <!-- Content Header -->
            <div class="bg-white border-b border-gray-200 p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 id="current-endpoint-title" class="text-2xl font-bold text-gray-800">Explorador de API SAP B1</h2>
                        <p id="current-endpoint-description" class="text-gray-600 mt-1">Selecciona un endpoint para comenzar a probar</p>
                    </div>
                    <div class="flex items-center gap-4">
                        <div class="text-sm text-gray-500">
                            <i class="fas fa-database"></i> <?php echo htmlspecialchars($config['company_db']); ?>
                        </div>
                        <div class="text-sm text-gray-500">
                            <i class="fas fa-user"></i> <?php echo htmlspecialchars($config['username']); ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Content Body -->
            <div class="flex-1 overflow-auto p-6">
                <!-- Welcome Screen -->
                <div id="welcome-screen" class="max-w-4xl mx-auto">
                    <div class="bg-white rounded-xl p-8 shadow-lg">
                        <div class="text-center mb-8">
                            <i class="fas fa-rocket text-6xl text-sap-blue mb-4"></i>
                            <h1 class="text-3xl font-bold text-gray-800 mb-4">Bienvenido al Explorador de API SAP B1</h1>
                            <p class="text-xl text-gray-600">Interfaz completa para probar todos los endpoints del Service Layer</p>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                            <div class="bg-blue-50 rounded-lg p-6 border-l-4 border-sap-blue">
                                <h3 class="font-semibold text-sap-blue mb-3 flex items-center gap-2">
                                    <i class="fas fa-info-circle"></i> Cómo usar esta herramienta
                                </h3>
                                <ol class="list-decimal ml-6 text-gray-700 space-y-2">
                                    <li>La conexión con SAP B1 se realiza automáticamente al cargar la página</li>
                                    <li>Selecciona un endpoint de la barra lateral izquierda</li>
                                    <li>Configura los parámetros y método HTTP según necesites</li>
                                </ol>
                            </div>
                            <div class="bg-green-50 rounded-lg p-6 border-l-4 border-green-600">
                                <h3 class="font-semibold text-green-600 mb-3 flex items-center gap-2">
                                    <i class="fas fa-info-circle"></i> Notas importantes
                                </h3>
                                <ol class="list-decimal ml-6 text-gray-700 space-y-2">
                                    <li>Este explorador es una herramienta de prueba y no debe usarse para producción.</li>
                                    <li>Los datos mostrados son simulados y no representan datos reales.</li>
                                    <li>Por favor, consulte la documentación oficial de SAP Business One para obtener más detalles sobre los endpoints disponibles.</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Batch Test Results Area -->
                <div id="batch-results" class="max-w-6xl mx-auto mt-6" style="display: none;">
                    <div class="bg-white rounded-xl p-6 shadow-lg">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                            <i class="fas fa-tasks"></i> Batch Test Results
                        </h3>
                        <!-- Batch results content will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
    // Debug: Check if JavaScript is loading
    console.log('JavaScript loaded successfully');
    
    // Toggle sidebar visibility
    function toggleSidebar() {
        var sidebar = document.getElementById('sidebar');
        var mainContent = document.getElementById('main-content');
        var sidebarToggle = document.getElementById('sidebar-toggle');
        
        if (sidebar && mainContent) {
            sidebar.classList.toggle('hidden');
            mainContent.classList.toggle('w-full');
            
            // Update toggle button icon
            if (sidebarToggle) {
                var icon = sidebarToggle.querySelector('i');
                if (icon) {
                    if (sidebar.classList.contains('hidden')) {
                        icon.className = 'fas fa-bars';
                    } else {
                        icon.className = 'fas fa-times';
                    }
                }
            }
        }
    }
    
    // Handle logout
    function logout() {
        if (confirm('¿Estás seguro de que quieres cerrar sesión?')) {
            // Redirect to logout endpoint
            window.location.href = '?action=logout';
        }
    }
    
    // Toggle the visibility of a category in the sidebar
    function toggleCategory(button) {
        // Find the next sibling (the endpoint list)
        var endpointList = button.nextElementSibling;
        if (!endpointList) return;
        // Toggle visibility
        endpointList.classList.toggle('hidden');
        // Toggle chevron rotation
        var chevron = button.querySelector('.fa-chevron-down');
        if (chevron) {
            chevron.classList.toggle('rotate-180');
        }
    }

    // Select an endpoint and populate the form
    function selectEndpoint(endpoint, info) {
        try {
            console.log('selectEndpoint called with:', endpoint, info);
            
            // Validate parameters
            if (!endpoint || !info) {
                console.error('Invalid parameters:', endpoint, info);
                return;
            }
            
            // Ensure info.method exists
            if (!info.method) {
                console.error('Missing method in info:', info);
                return;
            }
        
        // Update the main content area
        const titleElement = document.getElementById('current-endpoint-title');
        const descElement = document.getElementById('current-endpoint-description');

        if (titleElement) {
            titleElement.textContent = endpoint;
        }
        if (descElement) {
            descElement.textContent = info.description || 'Endpoint seleccionado';
        }

        // Hide welcome screen and show endpoint form
        const welcomeScreen = document.getElementById('welcome-screen');
        if (welcomeScreen) {
            welcomeScreen.style.display = 'none';
        }
        
        // Create or update the endpoint form
        const mainContent = document.querySelector('#main-content .flex-1.overflow-auto');
        let existingForm = document.getElementById('endpoint-form');

        if (!mainContent) {
            console.error('Main content area not found');
            return;
        }

        if (!existingForm) {
            var formHtml = `
                <div id="endpoint-form" class="max-w-4xl mx-auto">
                    <div class="bg-white rounded-xl p-6 shadow-lg">
                        <form id="endpoint-test-form" onsubmit="handleEndpointTest(event)">
                            <input type="hidden" name="action" value="test_endpoint">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Endpoint:</label>
                                    <input type="text" name="endpoint" value="${endpoint}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sap-blue" readonly>
                                    <div class="mt-1 text-xs text-gray-500">
                                        <strong>Full URL:</strong> <?php echo htmlspecialchars($config['base_url']); ?>${endpoint}
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Método HTTP:</label>
                                    <select name="method" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sap-blue">
                                        ${info.method.split(',').map(method => `<option value="${method.trim()}">${method.trim()}</option>`).join('')}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Parámetros (OData):</label>
                                <input type="text" name="params" placeholder="Ej: $top=10&$filter=CardType eq 'C'" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sap-blue">
                                <p class="text-xs text-gray-500 mt-1">Parámetros OData como $top, $filter, $select, etc.</p>
                            </div>
                            
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Cuerpo de la petición (JSON):</label>
                                <textarea name="body" rows="6" placeholder='{"key": "value"}' class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sap-blue"></textarea>
                                <p class="text-xs text-gray-500 mt-1">Solo necesario para POST, PATCH, PUT</p>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Página:</label>
                                    <input type="number" name="page" value="1" min="1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sap-blue">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Límite:</label>
                                    <input type="number" name="limit" value="20" min="1" max="100" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sap-blue">
                                </div>
                                <div class="flex items-end">
                                    <button type="submit" class="w-full bg-sap-blue text-white px-6 py-2 rounded-md hover:bg-blue-700 transition">
                                        <i class="fas fa-play mr-2"></i>Probar Endpoint
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        <div id="test-results" class="mt-6">
                            <!-- Results will be displayed here -->
                        </div>
                    </div>
                </div>
            `;
            mainContent.innerHTML = formHtml;
        } else {
            // Update existing form
            existingForm.querySelector('input[name="endpoint"]').value = endpoint;
            var methodSelect = existingForm.querySelector('select[name="method"]');
            methodSelect.innerHTML = info.method.split(',').map(method => `<option value="${method.trim()}">${method.trim()}</option>`).join('');
        }
        
        // Highlight selected endpoint in sidebar (if called from an event)
        try {
            document.querySelectorAll('.endpoint-item').forEach(item => {
                item.classList.remove('bg-sap-blue', 'text-white');
            });

            // Only try to highlight if we have an event context
            if (typeof event !== 'undefined' && event && event.target) {
                const endpointItem = event.target.closest('.endpoint-item');
                if (endpointItem) {
                    endpointItem.classList.add('bg-sap-blue', 'text-white');
                }
            }
        } catch (highlightError) {
            console.warn('Could not highlight endpoint item:', highlightError);
        }

        } catch (error) {
            console.error('Error in selectEndpoint:', error);
            alert('Error al seleccionar el endpoint: ' + error.message);
        }
    }

    // Load a predefined example and select the corresponding endpoint
    function loadExample(example) {
        // Map example keys to endpoint and parameters
        var examples = {
            'currencies': {
                endpoint: 'Currencies',
                method: 'GET',
                params: '',
                description: 'Monedas Disponibles'
            },
            'business-partners': {
                endpoint: 'BusinessPartners',
                method: 'GET',
                params: "$filter=CardType eq 'C' and Valid eq 'tYES'",
                description: 'Clientes Activos'
            },
            'items': {
                endpoint: 'Items',
                method: 'GET',
                params: "$filter=QuantityOnStock gt 0",
                description: 'Productos con Stock'
            },
            'warehouses': {
                endpoint: 'Warehouses',
                method: 'GET',
                params: '',
                description: 'Almacenes'
            },
            'fondos-fijos': {
                endpoint: 'Items',
                method: 'GET',
                params: "$filter=ItemType eq 'itFixedAssets'&$top=25",
                description: 'Fondos Fijos (Artículos de Activos Fijos) - Muestra los primeros 25 artículos configurados como activos fijos'
            },
            'chart-accounts': {
                endpoint: 'ChartOfAccounts',
                method: 'GET',
                params: "$select=Code,Name,AccountType,ActiveAccount,FatherAccountKey&$filter=ActiveAccount eq 'tYES'&$top=50",
                description: 'Plan de Cuentas Activas - Muestra las primeras 50 cuentas activas'
            }
        };
        var ex = examples[example];
        if (!ex) {
            alert('Ejemplo no reconocido: ' + example);
            return;
        }
        
        // Call selectEndpoint with the example data
        selectEndpoint(ex.endpoint, {
            method: ex.method,
            description: ex.description
        });
        
        // Set parameters after a short delay to ensure form is created
        setTimeout(function() {
            var paramsInput = document.querySelector('input[name="params"]');
            if (paramsInput && ex.params) {
                paramsInput.value = ex.params;
            }
        }, 100);
    }

    // Handle endpoint test form submission
    function handleEndpointTest(event) {
        event.preventDefault(); // Prevent form from submitting normally
        
        var form = event.target;
        var formData = new FormData(form);
        
        // Show loading state
        var submitButton = form.querySelector('button[type="submit"]');
        var originalText = submitButton.innerHTML;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Probando...';
        submitButton.disabled = true;
        
        // Convert FormData to URLSearchParams for POST
        var params = new URLSearchParams();
        for (var pair of formData.entries()) {
            params.append(pair[0], pair[1]);
        }
        
        // Make AJAX request
        fetch(window.location.href, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params.toString()
        })
        .then(response => response.json())
        .then(data => {
            // Handle JSON response from PHP
            var currentResults = document.querySelector('#test-results');
            if (currentResults) {
                if (data.success) {
                    // Success response
                    var resultHtml = '<div class="bg-green-50 border border-green-200 rounded-lg p-4">';
                    resultHtml += '<h3 class="text-green-800 font-semibold mb-2">✅ Petición Exitosa</h3>';
                    // Enhanced response display with full endpoint URL and metrics
                    resultHtml += '<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">';
                    resultHtml += '<div><strong>Código HTTP:</strong> <span class="text-green-600 font-mono">' + (data.http_code || 'N/A') + '</span></div>';
                    resultHtml += '<div><strong>Tiempo:</strong> <span class="text-blue-600 font-mono">' + (data.request_time ? (data.request_time * 1000).toFixed(0) + 'ms' : 'N/A') + '</span></div>';
                    resultHtml += '<div><strong>Cached:</strong> <span class="' + (data.cached ? 'text-orange-600' : 'text-gray-600') + ' font-mono">' + (data.cached ? 'Sí' : 'No') + '</span></div>';
                    resultHtml += '<div><strong>Request ID:</strong> <span class="text-purple-600 font-mono text-xs">' + (data.request_id || 'N/A') + '</span></div>';
                    resultHtml += '</div>';

                    // Add properly encoded full endpoint URL display
                    var currentForm = document.querySelector('#endpoint-test-form');
                    var endpointValue = currentForm ? currentForm.querySelector('input[name="endpoint"]').value : 'Unknown';
                    var methodValue = currentForm ? currentForm.querySelector('select[name="method"]').value : 'GET';
                    var paramsValue = currentForm ? currentForm.querySelector('input[name="params"]').value : '';

                    resultHtml += '<div class="mb-4 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">';
                    resultHtml += '<div class="flex items-center gap-2 mb-2">';
                    resultHtml += '<span class="px-2 py-1 bg-blue-600 text-white text-xs rounded font-mono">' + methodValue + '</span>';
                    resultHtml += '<strong class="text-blue-800">Full Endpoint URL:</strong>';
                    resultHtml += '</div>';

                    // Properly encode the URL
                    var baseUrl = '<?php echo htmlspecialchars($config['base_url']); ?>';
                    var fullUrl = baseUrl + encodeURIComponent(endpointValue).replace(/%2F/g, '/'); // Keep forward slashes

                    if (paramsValue) {
                        // Properly encode OData parameters
                        var encodedParams = encodeODataParams(paramsValue);
                        fullUrl += '?' + encodedParams;
                    }

                    resultHtml += '<div class="space-y-2">';
                    resultHtml += '<div><strong class="text-xs text-blue-600">Encoded URL:</strong></div>';
                    resultHtml += '<code class="text-sm text-blue-800 bg-white p-2 rounded border block overflow-x-auto break-all">' + fullUrl + '</code>';

                    if (paramsValue) {
                        resultHtml += '<div><strong class="text-xs text-blue-600">Original Parameters:</strong></div>';
                        resultHtml += '<code class="text-xs text-gray-600 bg-gray-100 p-2 rounded border block overflow-x-auto">' + paramsValue + '</code>';
                    }
                    resultHtml += '</div>';
                    resultHtml += '</div>';

                    if (data.data && data.data.value) {
                        var count = data.data.value.length;
                        resultHtml += '<div class="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-3">';
                        resultHtml += '<div class="flex items-center gap-4">';
                        resultHtml += '<p class="text-green-700"><strong>Registros encontrados:</strong> <span class="text-purple-600 font-bold text-lg">' + count + '</span></p>';

                        // Add pagination size selector
                        resultHtml += '<div class="flex items-center gap-2">';
                        resultHtml += '<label class="text-sm text-gray-600">Mostrar:</label>';
                        resultHtml += '<select onchange="changePaginationSize(this)" class="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">';
                        var pageSizes = [10, 25, 50, 100, 250, 500];
                        var currentPageSize = Math.min(maxRecords, count);
                        pageSizes.forEach(size => {
                            var selected = size === currentPageSize ? 'selected' : '';
                            resultHtml += '<option value="' + size + '" ' + selected + '>' + size + '</option>';
                        });
                        resultHtml += '</select>';
                        resultHtml += '<span class="text-sm text-gray-600">por página</span>';
                        resultHtml += '</div>';
                        resultHtml += '</div>';

                        resultHtml += '<div class="flex gap-2 flex-wrap">';
                        resultHtml += '<button onclick="toggleJsonView(this)" class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition">Ver JSON</button>';
                        resultHtml += '<button onclick="exportTableData(this)" class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition">Exportar</button>';
                        resultHtml += '<button onclick="openFullScreenTable(this)" class="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700 transition"><i class="fas fa-expand"></i> Pantalla Completa</button>';
                        resultHtml += '</div>';
                        resultHtml += '</div>';

                        if (count > 0) {
                            // Enhanced table with search and controls
                            resultHtml += '<div class="table-container" data-table-id="' + Date.now() + '">';
                            resultHtml += '<div class="mb-3 flex gap-2 items-center flex-wrap">';
                            resultHtml += '<div class="flex-1 min-w-64">';
                            resultHtml += '<div class="relative">';
                            resultHtml += '<input type="text" placeholder="🔍 Buscar en tabla..." onkeyup="filterTable(this)" class="w-full px-3 py-2 pl-8 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">';
                            resultHtml += '<i class="fas fa-search absolute left-2 top-3 text-gray-400 text-xs"></i>';
                            resultHtml += '</div>';
                            resultHtml += '</div>';
                            resultHtml += '<button onclick="clearTableFilter(this)" class="px-3 py-2 bg-gray-500 text-white text-sm rounded hover:bg-gray-600 transition">Limpiar</button>';
                            resultHtml += '<button onclick="generatePostmanCollection(this)" class="px-3 py-2 bg-orange-600 text-white text-sm rounded hover:bg-orange-700 transition"><i class="fas fa-rocket"></i> Postman</button>';
                            resultHtml += '</div>';
                            resultHtml += '<div class="overflow-x-auto border rounded-lg shadow-sm">';
                            resultHtml += '<table class="data-table w-full">';

                            // Enhanced headers with sorting and double-click info
                            var firstRecord = data.data.value[0];
                            var headerIndex = 0;
                            resultHtml += '<thead><tr>';
                            for (var key in firstRecord) {
                                resultHtml += '<th class="bg-gray-100 px-4 py-3 text-left font-semibold text-gray-700 border-b-2 border-gray-200 sticky top-0 sortable cursor-pointer hover:bg-gray-200 transition select-none" ';
                                resultHtml += 'onclick="sortTableColumn(' + headerIndex + ', this)" ';
                                resultHtml += 'title="Click para ordenar • Doble click en filas para ver detalles" ';
                                resultHtml += 'data-column="' + headerIndex + '" data-sort-direction="none">';
                                resultHtml += '<div class="flex items-center justify-between">';
                                resultHtml += '<span class="truncate">' + key + '</span>';
                                resultHtml += '<div class="flex flex-col ml-1">';
                                resultHtml += '<i class="fas fa-caret-up text-gray-300 text-xs leading-none sort-up"></i>';
                                resultHtml += '<i class="fas fa-caret-down text-gray-300 text-xs leading-none sort-down"></i>';
                                resultHtml += '</div>';
                                resultHtml += '</div>';
                                resultHtml += '</th>';
                                headerIndex++;
                            }
                            resultHtml += '</tr></thead>';

                            // Enhanced rows with double-click support and better data formatting
                            resultHtml += '<tbody>';
                            var maxRecords = Math.min(count, 15); // Show more records
                            for (var i = 0; i < maxRecords; i++) {
                                var rowData = data.data.value[i];
                                resultHtml += '<tr class="hover:bg-blue-50 cursor-pointer transition-colors duration-200" ';
                                resultHtml += 'ondblclick="showItemDetails(this)" ';
                                resultHtml += 'data-row-index="' + i + '" ';
                                resultHtml += 'data-item="' + encodeURIComponent(JSON.stringify(rowData)) + '" ';
                                resultHtml += 'title="Doble click para ver detalles completos">';

                                for (var key in firstRecord) {
                                    var value = rowData[key];
                                    var displayValue = '';
                                    var cellClass = 'px-4 py-3 border-b border-gray-200 text-sm';

                                    if (value === null || value === undefined) {
                                        displayValue = '<span class="text-gray-400 italic">null</span>';
                                    } else if (typeof value === 'object') {
                                        displayValue = '<span class="text-blue-600 font-medium cursor-pointer" onclick="event.stopPropagation(); showObjectDetails(this)" data-object="' + encodeURIComponent(JSON.stringify(value)) + '" title="Click to view object details">📋 Object</span>';
                                    } else if (typeof value === 'boolean') {
                                        displayValue = '<span class="' + (value ? 'text-green-600' : 'text-red-600') + ' font-medium">';
                                        displayValue += '<i class="fas fa-' + (value ? 'check-circle' : 'times-circle') + '"></i> ' + value;
                                        displayValue += '</span>';
                                    } else if (typeof value === 'number') {
                                        displayValue = '<span class="text-purple-600 font-mono">' + value.toLocaleString() + '</span>';
                                    } else if (typeof value === 'string' && (value.includes('http') || value.includes('www'))) {
                                        // Handle URLs
                                        displayValue = '<a href="' + value + '" target="_blank" class="text-blue-600 hover:underline" onclick="event.stopPropagation()">🔗 Link</a>';
                                    } else if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}/)) {
                                        // Handle dates
                                        displayValue = '<span class="text-indigo-600 font-mono">📅 ' + value + '</span>';
                                    } else {
                                        var stringValue = String(value);
                                        if (stringValue.length > 50) {
                                            displayValue = '<span class="cursor-pointer" title="' + stringValue.replace(/"/g, '&quot;') + '" onclick="event.stopPropagation(); showFullText(this)">';
                                            displayValue += stringValue.substring(0, 50) + '<span class="text-blue-500 font-bold">...</span></span>';
                                        } else {
                                            displayValue = '<span class="break-words">' + stringValue + '</span>';
                                        }
                                    }
                                    resultHtml += '<td class="' + cellClass + '">' + displayValue + '</td>';
                                }
                                resultHtml += '</tr>';
                            }
                            resultHtml += '</tbody></table>';
                            resultHtml += '</div>';

                            // Enhanced pagination and navigation controls
                            var currentParams = currentForm ? currentForm.querySelector('input[name="params"]').value : '';
                            var currentTop = extractTopValue(currentParams) || maxRecords;
                            var currentSkip = extractSkipValue(currentParams) || 0;
                            var currentPage = Math.floor(currentSkip / currentTop) + 1;
                            var totalPages = Math.ceil(count / currentTop);
                            var hasMorePages = count > maxRecords;

                            resultHtml += '<div class="mt-3 space-y-3">';

                            // Pagination info and controls
                            resultHtml += '<div class="flex flex-col sm:flex-row sm:items-center justify-between gap-3 text-sm">';
                            resultHtml += '<div class="flex items-center gap-4">';
                            resultHtml += '<span class="text-gray-600">Mostrando <strong class="text-blue-600">' + Math.min(maxRecords, count) + '</strong> de <strong class="text-purple-600">' + count + '</strong> registros</span>';

                            if (hasMorePages) {
                                resultHtml += '<span class="text-orange-600">• Página <strong>' + currentPage + '</strong> de <strong>' + totalPages + '</strong></span>';
                            }
                            resultHtml += '</div>';

                            // Pagination navigation
                            if (hasMorePages) {
                                resultHtml += '<div class="flex items-center gap-2">';

                                // Previous page
                                if (currentPage > 1) {
                                    resultHtml += '<button onclick="navigateToPage(' + (currentPage - 1) + ', ' + currentTop + ')" class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded hover:bg-blue-200 transition">← Anterior</button>';
                                }

                                // Page numbers (show current and nearby pages)
                                var startPage = Math.max(1, currentPage - 2);
                                var endPage = Math.min(totalPages, currentPage + 2);

                                for (var p = startPage; p <= endPage; p++) {
                                    var pageClass = p === currentPage ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200';
                                    resultHtml += '<button onclick="navigateToPage(' + p + ', ' + currentTop + ')" class="px-2 py-1 ' + pageClass + ' text-xs rounded transition">' + p + '</button>';
                                }

                                // Next page
                                if (currentPage < totalPages) {
                                    resultHtml += '<button onclick="navigateToPage(' + (currentPage + 1) + ', ' + currentTop + ')" class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded hover:bg-blue-200 transition">Siguiente →</button>';
                                }

                                resultHtml += '</div>';
                            }
                            resultHtml += '</div>';

                            // Selection controls
                            resultHtml += '<div class="flex items-center justify-between text-xs text-gray-500">';
                            resultHtml += '<div class="flex items-center gap-2">';
                            resultHtml += '<button onclick="selectAllTableRows(this)" class="px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition">Seleccionar Todo</button>';
                            resultHtml += '<span id="selected-count">0 seleccionados</span>';
                            resultHtml += '</div>';

                            if (hasMorePages) {
                                resultHtml += '<div class="flex items-center gap-2">';
                                resultHtml += '<span>Ir a página:</span>';
                                resultHtml += '<input type="number" min="1" max="' + totalPages + '" value="' + currentPage + '" onchange="navigateToPage(this.value, ' + currentTop + ')" class="w-16 px-1 py-1 border border-gray-300 rounded text-center">';
                                resultHtml += '<span>de ' + totalPages + '</span>';
                                resultHtml += '</div>';
                            }
                            resultHtml += '</div>';
                            resultHtml += '</div>';

                            if (count > maxRecords) {
                                resultHtml += '<div class="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">';
                                resultHtml += '<div class="flex items-center justify-between">';
                                resultHtml += '<div>';
                                resultHtml += '<p class="text-blue-800 text-sm"><strong>💡 Tip:</strong> Para ver más registros, use parámetros OData:</p>';
                                resultHtml += '<ul class="text-blue-700 text-xs mt-1 ml-4">';
                                resultHtml += '<li>• <code>$top=50</code> - Mostrar 50 registros</li>';
                                resultHtml += '<li>• <code>$skip=10</code> - Saltar los primeros 10</li>';
                                resultHtml += '<li>• <code>$top=20&$skip=20</code> - Página 2 de 20 registros</li>';
                                resultHtml += '</ul>';
                                resultHtml += '</div>';
                                resultHtml += '<button onclick="showPaginationHelper(this)" class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition">Ayuda Paginación</button>';
                                resultHtml += '</div>';
                                resultHtml += '</div>';
                            }
                            resultHtml += '</div>';

                            // Hidden JSON view
                            resultHtml += '<div class="json-view mt-4" style="display: none;">';
                            resultHtml += '<h4 class="font-semibold text-gray-800 mb-2">Respuesta JSON Completa</h4>';
                            resultHtml += '<pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto max-h-96 overflow-y-auto"><code>' + JSON.stringify(data.data, null, 2) + '</code></pre>';
                            resultHtml += '</div>';
                        }
                    } else {
                        // Non-array response
                        resultHtml += '<div class="mt-4">';
                        resultHtml += '<h4 class="font-semibold text-gray-800 mb-2">Respuesta JSON</h4>';
                        resultHtml += '<pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto max-h-96 overflow-y-auto"><code>' + JSON.stringify(data.data, null, 2) + '</code></pre>';
                        resultHtml += '</div>';
                    }
                    
                    if (data.pagination) {
                        resultHtml += '<div class="mt-3 p-2 bg-blue-50 rounded"><p class="text-sm text-blue-700"><strong>Paginación:</strong> Página ' + data.pagination.page + ', Límite: ' + data.pagination.limit + '</p></div>';
                    }
                    
                    resultHtml += '</div>';
                    currentResults.innerHTML = resultHtml;
                } else {
                    // Error response
                    var errorHtml = '<div class="bg-red-50 border border-red-200 rounded-lg p-4">';
                    errorHtml += '<h3 class="text-red-800 font-semibold mb-2">❌ Error en la Petición</h3>';
                    errorHtml += '<p class="text-red-700 mb-2"><strong>Código HTTP:</strong> ' + (data.http_code || 'N/A') + '</p>';
                    
                    if (data.message) {
                        errorHtml += '<p class="text-red-700 mb-2"><strong>Mensaje:</strong> ' + data.message + '</p>';
                    }
                    
                    if (data.error) {
                        errorHtml += '<p class="text-red-700 mb-2"><strong>Error:</strong> ' + data.error + '</p>';
                    }
                    
                    if (data.data && data.data.error) {
                        errorHtml += '<p class="text-red-700 mb-2"><strong>Error SAP:</strong> ' + data.data.error.message.value + '</p>';
                    }
                    
                    errorHtml += '</div>';
                    currentResults.innerHTML = errorHtml;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            var currentResults = document.querySelector('#test-results');
            if (currentResults) {
                currentResults.innerHTML = '<div class="bg-red-50 border border-red-200 rounded-lg p-4"><p class="text-red-800">Error al procesar la petición:</p><pre class="mt-2 text-sm">' + error.message + '</pre></div>';
            }
        })
        .finally(() => {
            // Restore button state
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        });
    }

    // Enhanced functionality for performance monitoring
    class PerformanceMonitor {
        constructor() {
            this.metrics = {};
            this.startTime = performance.now();
        }

        start(operation) {
            this.metrics[operation] = {
                startTime: performance.now(),
                startMemory: performance.memory ? performance.memory.usedJSHeapSize : 0
            };
        }

        end(operation) {
            if (this.metrics[operation]) {
                this.metrics[operation].endTime = performance.now();
                this.metrics[operation].duration = this.metrics[operation].endTime - this.metrics[operation].startTime;
                this.metrics[operation].endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
                this.metrics[operation].memoryDelta = this.metrics[operation].endMemory - this.metrics[operation].startMemory;
            }
        }

        getMetrics() {
            return this.metrics;
        }

        displayMetrics() {
            const metricsDiv = document.getElementById('performance-metrics');
            if (!metricsDiv) {
                console.warn('Performance metrics div not found');
                return;
            }

            const metricsCount = Object.keys(this.metrics).length;

            if (metricsCount === 0) {
                metricsDiv.innerHTML = `
                    <h4 class="text-lg font-semibold mb-2">Performance Metrics</h4>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <p class="text-yellow-800">No performance metrics available yet. Try testing some endpoints first!</p>
                    </div>
                `;
                return;
            }

            let html = '<h4 class="text-lg font-semibold mb-2">Performance Metrics</h4>';
            html += `<p class="text-sm text-gray-600 mb-4">Showing ${metricsCount} operations</p>`;
            html += '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';

            for (const [operation, data] of Object.entries(this.metrics)) {
                if (data.duration !== undefined) {
                    html += `
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <h5 class="font-medium text-blue-800">${operation}</h5>
                            <p class="text-sm text-blue-600">Duration: ${data.duration.toFixed(2)}ms</p>
                            ${data.memoryDelta ? `<p class="text-sm text-blue-600">Memory: ${(data.memoryDelta / 1024).toFixed(2)}KB</p>` : ''}
                            <p class="text-xs text-gray-500">Started: ${new Date(data.startTime + performance.timeOrigin).toLocaleTimeString()}</p>
                        </div>
                    `;
                }
            }

            html += '</div>';
            html += `
                <div class="mt-4 text-center">
                    <button onclick="perfMonitor.clearMetrics()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition">
                        <i class="fas fa-trash"></i> Clear Metrics
                    </button>
                </div>
            `;

            metricsDiv.innerHTML = html;
        }

        clearMetrics() {
            this.metrics = {};
            console.log('Performance metrics cleared');
            this.displayMetrics();
        }
    }

    // Initialize performance monitor
    const perfMonitor = new PerformanceMonitor();

    // Start tracking page load
    perfMonitor.start('page_load');

    // OData parameter encoding function
    function encodeODataParams(params) {
        if (!params) return '';

        // Split parameters by & but be careful with quotes
        const paramPairs = [];
        let currentParam = '';
        let inQuotes = false;

        for (let i = 0; i < params.length; i++) {
            const char = params[i];

            if (char === "'" && (i === 0 || params[i-1] !== '\\')) {
                inQuotes = !inQuotes;
            }

            if (char === '&' && !inQuotes) {
                if (currentParam.trim()) {
                    paramPairs.push(currentParam.trim());
                }
                currentParam = '';
            } else {
                currentParam += char;
            }
        }

        if (currentParam.trim()) {
            paramPairs.push(currentParam.trim());
        }

        // Encode each parameter properly
        const encodedPairs = paramPairs.map(pair => {
            const [key, ...valueParts] = pair.split('=');
            if (valueParts.length === 0) return encodeURIComponent(key);

            const value = valueParts.join('=');

            // Special handling for OData operators
            let encodedValue = value
                .replace(/'/g, '%27')  // Single quotes
                .replace(/ eq /g, '%20eq%20')  // Equal operator
                .replace(/ ne /g, '%20ne%20')  // Not equal
                .replace(/ gt /g, '%20gt%20')  // Greater than
                .replace(/ ge /g, '%20ge%20')  // Greater or equal
                .replace(/ lt /g, '%20lt%20')  // Less than
                .replace(/ le /g, '%20le%20')  // Less or equal
                .replace(/ and /g, '%20and%20')  // And operator
                .replace(/ or /g, '%20or%20')   // Or operator
                .replace(/ not /g, '%20not%20') // Not operator
                .replace(/ /g, '%20');          // Spaces

            return encodeURIComponent(key) + '=' + encodedValue;
        });

        return encodedPairs.join('&');
    }

    // Enhanced request function with performance tracking
    function makeEnhancedRequest(url, options = {}) {
        const requestId = 'request_' + Date.now();
        perfMonitor.start(requestId);

        return fetch(url, options)
            .then(response => {
                perfMonitor.end(requestId);
                return response;
            })
            .catch(error => {
                perfMonitor.end(requestId);
                throw error;
            });
    }

    // Auto-refresh session status
    function checkSessionStatus(skipInitialCheck = false) {
        // If this is the initial check and we have a PHP session result, use it
        if (!skipInitialCheck && typeof window.phpSessionStatus !== 'undefined') {
            updateSessionStatusDisplay(window.phpSessionStatus);
            return;
        }

        makeEnhancedRequest(window.location.href, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=check_session'
        })
        .then(response => response.json())
        .then(data => {
            updateSessionStatusDisplay(data.valid);
        })
        .catch(error => {
            console.error('Session check failed:', error);
            updateSessionStatusDisplay(false);
        });
    }

    // Helper function to update session status display
    function updateSessionStatusDisplay(isValid) {
        const statusIndicator = document.getElementById('session-status');
        const sessionTime = document.getElementById('session-time');

        if (statusIndicator) {
            if (isValid) {
                statusIndicator.innerHTML = '✓ Session Active';
                statusIndicator.className = 'text-sm font-medium text-green-600';

                // Update timestamp if element exists
                if (sessionTime) {
                    const now = new Date();
                    sessionTime.textContent = now.toLocaleTimeString();
                }
            } else {
                statusIndicator.innerHTML = '✗ Session Expired';
                statusIndicator.className = 'text-sm font-medium text-red-600';
            }
        }
    }

    // Batch testing functionality
    function runBatchTests() {
        const selectedEndpoints = Array.from(document.querySelectorAll('input[name="batch_endpoints"]:checked'))
            .map(cb => cb.value);

        if (selectedEndpoints.length === 0) {
            alert('Please select at least one endpoint for batch testing');
            return;
        }

        const batchResults = document.getElementById('batch-results');
        if (batchResults) {
            batchResults.style.display = 'block';
            batchResults.querySelector('div').innerHTML = '<div class="text-center py-4">Running batch tests...</div>';
        }

        const results = [];
        let completed = 0;

        selectedEndpoints.forEach((endpoint, index) => {
            setTimeout(() => {
                perfMonitor.start(`batch_${endpoint}`);

                makeEnhancedRequest(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=test_endpoint&endpoint=${encodeURIComponent(endpoint)}&method=GET&limit=5`
                })
                .then(response => response.json())
                .then(data => {
                    perfMonitor.end(`batch_${endpoint}`);
                    results.push({
                        endpoint: endpoint,
                        success: data.success,
                        http_code: data.http_code,
                        response_time: data.request_time || 0
                    });

                    completed++;
                    updateBatchProgress(completed, selectedEndpoints.length, results);
                })
                .catch(error => {
                    perfMonitor.end(`batch_${endpoint}`);
                    results.push({
                        endpoint: endpoint,
                        success: false,
                        error: error.message
                    });

                    completed++;
                    updateBatchProgress(completed, selectedEndpoints.length, results);
                });
            }, index * 500); // Stagger requests by 500ms
        });
    }

    function updateBatchProgress(completed, total, results) {
        const batchResults = document.getElementById('batch-results');
        if (!batchResults) return;

        const contentDiv = batchResults.querySelector('div');

        const progress = (completed / total) * 100;
        let html = `
            <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Progress</span>
                    <span>${completed}/${total}</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full" style="width: ${progress}%"></div>
                </div>
            </div>
        `;

        if (completed === total) {
            html += '<h4 class="text-lg font-semibold mb-3">Batch Test Results</h4>';
            html += '<div class="space-y-2">';

            results.forEach(result => {
                const statusClass = result.success ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800';
                const statusIcon = result.success ? '✓' : '✗';

                html += `
                    <div class="border rounded-lg p-3 ${statusClass}">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">${statusIcon} ${result.endpoint}</span>
                            <div class="text-sm">
                                ${result.http_code ? `HTTP ${result.http_code}` : ''}
                                ${result.response_time ? ` | ${(result.response_time * 1000).toFixed(0)}ms` : ''}
                            </div>
                        </div>
                        ${result.error ? `<div class="text-sm mt-1">Error: ${result.error}</div>` : ''}
                    </div>
                `;
            });

            html += '</div>';

            // Show performance metrics
            perfMonitor.displayMetrics();
        }

        if (contentDiv) {
            contentDiv.innerHTML = html;
        }
    }

    // Export functionality
    function exportResults(format) {
        const results = document.getElementById('test-results');
        if (!results) {
            alert('No results to export');
            return;
        }

        const data = {
            timestamp: new Date().toISOString(),
            results: results.innerHTML,
            performance: perfMonitor.getMetrics()
        };

        if (format === 'json') {
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            downloadBlob(blob, `sap_api_results_${Date.now()}.json`);
        } else if (format === 'html') {
            const htmlContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>SAP B1 API Test Results</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .timestamp { color: #666; font-size: 0.9em; }
                        .results { margin-top: 20px; }
                    </style>
                </head>
                <body>
                    <h1>SAP B1 API Test Results</h1>
                    <div class="timestamp">Generated: ${data.timestamp}</div>
                    <div class="results">${data.results}</div>
                </body>
                </html>
            `;
            const blob = new Blob([htmlContent], { type: 'text/html' });
            downloadBlob(blob, `sap_api_results_${Date.now()}.html`);
        }
    }

    function downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Helper functions for new UI elements
    function clearCache() {
        if (confirm('Are you sure you want to clear the API response cache?')) {
            makeEnhancedRequest(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=clear_cache'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Cache cleared successfully! ${data.files_cleared} files removed.`);
                } else {
                    alert('Failed to clear cache');
                }
            })
            .catch(error => {
                alert('Error clearing cache: ' + error.message);
            });
        }
    }

    function selectBatchEndpoints(type) {
        const checkboxes = document.querySelectorAll('input[name="batch_endpoints"]');

        switch(type) {
            case 'common':
                checkboxes.forEach(cb => {
                    cb.checked = ['BusinessPartners', 'Items', 'Orders', 'Warehouses'].includes(cb.value);
                });
                break;
            case 'all':
                checkboxes.forEach(cb => cb.checked = true);
                break;
            case 'none':
                checkboxes.forEach(cb => cb.checked = false);
                break;
        }
    }

    // Enhanced sidebar toggle with animation
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const toggleIcon = document.querySelector('#sidebar-toggle i');
        const currentWidth = sidebar.style.width;

        if (currentWidth === '50px' || sidebar.classList.contains('collapsed')) {
            // Expand
            sidebar.style.width = '350px';
            sidebar.classList.remove('collapsed');
            toggleIcon.className = 'fas fa-chevron-left';

            // Show content with delay
            setTimeout(() => {
                const content = sidebar.querySelectorAll('.p-4 > *:not(#resize-handle)');
                content.forEach(el => el.style.display = 'block');
            }, 150);
        } else {
            // Collapse
            sidebar.style.width = '50px';
            sidebar.classList.add('collapsed');
            toggleIcon.className = 'fas fa-chevron-right';

            // Hide content immediately
            const content = sidebar.querySelectorAll('.p-4 > *:not(#resize-handle)');
            content.forEach(el => el.style.display = 'none');
        }
    }

    // Enhanced performance metrics display
    function togglePerformanceMetrics() {
        const metricsDiv = document.getElementById('performance-metrics');
        if (metricsDiv) {
            const isHidden = metricsDiv.style.display === 'none' || !metricsDiv.style.display;

            if (isHidden) {
                metricsDiv.style.display = 'block';
                perfMonitor.displayMetrics();

                // Update button text
                const button = document.querySelector('button[onclick="togglePerformanceMetrics()"]');
                if (button) {
                    button.innerHTML = '<i class="fas fa-chart-line"></i> Hide Performance Metrics';
                }
            } else {
                metricsDiv.style.display = 'none';

                // Update button text
                const button = document.querySelector('button[onclick="togglePerformanceMetrics()"]');
                if (button) {
                    button.innerHTML = '<i class="fas fa-chart-line"></i> Show Performance Metrics';
                }
            }
        }
    }

    function showPerformanceMetrics() {
        togglePerformanceMetrics();
    }

    // Enhanced JSON and table interaction functions
    function toggleJsonView(button) {
        const container = button.closest('.bg-green-50');
        const tableContainer = container.querySelector('.table-container');
        const jsonView = container.querySelector('.json-view');

        if (jsonView.style.display === 'none') {
            tableContainer.style.display = 'none';
            jsonView.style.display = 'block';
            button.textContent = 'Ver Tabla';
            button.className = button.className.replace('bg-blue-600 hover:bg-blue-700', 'bg-gray-600 hover:bg-gray-700');
        } else {
            tableContainer.style.display = 'block';
            jsonView.style.display = 'none';
            button.textContent = 'Ver JSON';
            button.className = button.className.replace('bg-gray-600 hover:bg-gray-700', 'bg-blue-600 hover:bg-blue-700');
        }
    }

    function showObjectDetails(element) {
        const objectData = JSON.parse(decodeURIComponent(element.getAttribute('data-object')));
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-2xl max-h-96 overflow-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Object Details</h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <pre class="bg-gray-100 p-3 rounded text-sm overflow-auto"><code>${JSON.stringify(objectData, null, 2)}</code></pre>
            </div>
        `;
        document.body.appendChild(modal);
    }

    function showFullText(element) {
        const fullText = element.getAttribute('title');
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-2xl max-h-96 overflow-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Full Text</h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="bg-gray-100 p-3 rounded text-sm overflow-auto">${fullText}</div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    function exportTableData(button) {
        const container = button.closest('.bg-green-50');
        const table = container.querySelector('table');

        if (!table) {
            alert('No table data to export');
            return;
        }

        // Convert table to CSV
        let csv = '';
        const rows = table.querySelectorAll('tr');

        rows.forEach(row => {
            const cells = row.querySelectorAll('th, td');
            const rowData = Array.from(cells).map(cell => {
                let text = cell.textContent.trim();
                // Handle special cases
                if (text === 'null') text = '';
                if (text === 'Object') text = '[Object]';
                // Escape quotes and wrap in quotes if contains comma
                if (text.includes(',') || text.includes('"')) {
                    text = '"' + text.replace(/"/g, '""') + '"';
                }
                return text;
            });
            csv += rowData.join(',') + '\n';
        });

        // Download CSV
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'sap_data_export_' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Full-screen table functionality
    function openFullScreenTable(button) {
        const container = button.closest('.bg-green-50');
        const table = container.querySelector('table.data-table');
        const endpointInfo = container.querySelector('.bg-blue-50');

        if (!table) {
            alert('No table data to display in full screen');
            return;
        }

        // Clone the table and endpoint info
        const tableClone = table.cloneNode(true);
        const endpointClone = endpointInfo ? endpointInfo.cloneNode(true) : null;

        // Create full-screen modal
        const modal = document.createElement('div');
        modal.className = 'fullscreen-modal';

        // Create modal content
        modal.innerHTML = `
            <button class="close-fullscreen" onclick="closeFullScreenTable()">
                <i class="fas fa-times"></i> Cerrar
            </button>
            <div class="modal-content">
                <div class="p-4 border-b bg-gray-50">
                    <div class="flex justify-between items-center">
                        <h2 class="text-xl font-bold text-gray-800">
                            <i class="fas fa-table"></i> Vista de Tabla - Pantalla Completa
                        </h2>
                        <div class="flex gap-2">
                            <button onclick="exportFullScreenTable()" class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition">
                                <i class="fas fa-download"></i> Exportar
                            </button>
                            <button onclick="toggleFullScreenColumns()" class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition">
                                <i class="fas fa-expand"></i> Expandir Columnas
                            </button>
                            <button onclick="showColumnSelector()" class="px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 transition">
                                <i class="fas fa-eye"></i> Columnas
                            </button>
                            <button onclick="resetTableView()" class="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
                <div class="p-4 bg-gray-50 border-b">
                    <div id="fullscreen-endpoint-info"></div>
                </div>
                <div class="table-container flex-1 p-4">
                    <div id="fullscreen-table-container"></div>
                </div>
            </div>
        `;

        // Add the modal to the page
        document.body.appendChild(modal);

        // Insert the cloned content
        if (endpointClone) {
            document.getElementById('fullscreen-endpoint-info').appendChild(endpointClone);
        }
        document.getElementById('fullscreen-table-container').appendChild(tableClone);

        // Prevent body scrolling
        document.body.style.overflow = 'hidden';

        // Add escape key listener
        document.addEventListener('keydown', handleFullScreenEscape);

        // Add click outside to close
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeFullScreenTable();
            }
        });

        console.log('Full-screen table opened');
    }

    function closeFullScreenTable() {
        const modal = document.querySelector('.fullscreen-modal');
        if (modal) {
            modal.remove();
            document.body.style.overflow = 'auto';
            document.removeEventListener('keydown', handleFullScreenEscape);
            console.log('Full-screen table closed');
        }
    }

    function handleFullScreenEscape(e) {
        if (e.key === 'Escape') {
            closeFullScreenTable();
        }
    }

    function exportFullScreenTable() {
        const table = document.querySelector('.fullscreen-modal table');
        if (table) {
            // Create a temporary button to reuse existing export function
            const tempButton = document.createElement('button');
            const tempContainer = document.createElement('div');
            tempContainer.className = 'bg-green-50';
            tempContainer.appendChild(table.cloneNode(true));
            tempButton.closest = () => tempContainer;

            exportTableData(tempButton);
        }
    }

    function toggleFullScreenColumns() {
        const table = document.querySelector('.fullscreen-modal table');
        const button = document.querySelector('.fullscreen-modal button[onclick="toggleFullScreenColumns()"]');

        if (!table) {
            console.error('No table found in full-screen modal');
            return;
        }

        const cells = table.querySelectorAll('th, td');
        const isCompact = table.classList.contains('compact-columns');

        if (isCompact) {
            // Expand columns - Auto width
            table.classList.remove('compact-columns');
            table.classList.add('expanded-columns');

            cells.forEach(cell => {
                cell.style.maxWidth = 'none';
                cell.style.minWidth = '120px';
                cell.style.whiteSpace = 'nowrap';
                cell.style.overflow = 'visible';
                cell.style.textOverflow = 'clip';
                cell.style.width = 'auto';
            });

            // Update button text
            if (button) {
                button.innerHTML = '<i class="fas fa-compress"></i> Compactar Columnas';
                button.className = button.className.replace('bg-blue-600 hover:bg-blue-700', 'bg-orange-600 hover:bg-orange-700');
            }

            console.log('Columns expanded');
        } else {
            // Compact columns - Fixed width
            table.classList.add('compact-columns');
            table.classList.remove('expanded-columns');

            cells.forEach(cell => {
                cell.style.maxWidth = '180px';
                cell.style.minWidth = '100px';
                cell.style.whiteSpace = 'nowrap';
                cell.style.overflow = 'hidden';
                cell.style.textOverflow = 'ellipsis';
                cell.style.width = '180px';
            });

            // Update button text
            if (button) {
                button.innerHTML = '<i class="fas fa-expand"></i> Expandir Columnas';
                button.className = button.className.replace('bg-orange-600 hover:bg-orange-700', 'bg-blue-600 hover:bg-blue-700');
            }

            console.log('Columns compacted');
        }

        // Force table layout recalculation
        table.style.tableLayout = 'fixed';
        setTimeout(() => {
            table.style.tableLayout = isCompact ? 'auto' : 'fixed';
        }, 100);
    }

    // Column selector functionality
    function showColumnSelector() {
        const table = document.querySelector('.fullscreen-modal table');
        if (!table) return;

        const headers = Array.from(table.querySelectorAll('thead th')).map((th, index) => ({
            index: index,
            text: th.textContent.trim(),
            visible: !th.classList.contains('hidden-column')
        }));

        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.style.zIndex = '10000'; // Higher than fullscreen modal

        let checkboxes = '';
        headers.forEach(header => {
            checkboxes += `
                <label class="flex items-center gap-2 p-2 hover:bg-gray-50 rounded">
                    <input type="checkbox" ${header.visible ? 'checked' : ''}
                           onchange="toggleColumn(${header.index}, this.checked)"
                           class="rounded border-gray-300">
                    <span class="text-sm">${header.text}</span>
                </label>
            `;
        });

        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-96 overflow-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Seleccionar Columnas</h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-1 max-h-64 overflow-y-auto">
                    ${checkboxes}
                </div>
                <div class="mt-4 flex gap-2">
                    <button onclick="selectAllColumns(true)" class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                        Todas
                    </button>
                    <button onclick="selectAllColumns(false)" class="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700">
                        Ninguna
                    </button>
                    <button onclick="this.closest('.fixed').remove()" class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 ml-auto">
                        Cerrar
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    function toggleColumn(columnIndex, visible) {
        const table = document.querySelector('.fullscreen-modal table');
        if (!table) return;

        const headers = table.querySelectorAll('thead th');
        const rows = table.querySelectorAll('tbody tr');

        if (headers[columnIndex]) {
            if (visible) {
                headers[columnIndex].classList.remove('hidden-column');
            } else {
                headers[columnIndex].classList.add('hidden-column');
            }
        }

        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells[columnIndex]) {
                if (visible) {
                    cells[columnIndex].classList.remove('hidden-column');
                } else {
                    cells[columnIndex].classList.add('hidden-column');
                }
            }
        });
    }

    function selectAllColumns(visible) {
        const table = document.querySelector('.fullscreen-modal table');
        if (!table) return;

        const headers = table.querySelectorAll('thead th');
        const checkboxes = document.querySelectorAll('.fixed input[type="checkbox"]');

        headers.forEach((header, index) => {
            toggleColumn(index, visible);
            if (checkboxes[index]) {
                checkboxes[index].checked = visible;
            }
        });
    }

    function resetTableView() {
        const table = document.querySelector('.fullscreen-modal table');
        if (!table) return;

        // Reset all columns to visible
        const allCells = table.querySelectorAll('th, td');
        allCells.forEach(cell => {
            cell.classList.remove('hidden-column');
            cell.style.maxWidth = '';
            cell.style.minWidth = '';
            cell.style.width = '';
            cell.style.whiteSpace = 'nowrap';
            cell.style.overflow = 'hidden';
            cell.style.textOverflow = 'ellipsis';
        });

        // Reset table classes
        table.classList.remove('compact-columns', 'expanded-columns');
        table.style.tableLayout = 'auto';

        // Reset button text
        const button = document.querySelector('.fullscreen-modal button[onclick="toggleFullScreenColumns()"]');
        if (button) {
            button.innerHTML = '<i class="fas fa-expand"></i> Expandir Columnas';
            button.className = 'px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition';
        }

        console.log('Table view reset');
    }

    // Table filtering functionality
    function filterTable(input) {
        const searchTerm = input.value.toLowerCase();
        const table = input.closest('.table-container').querySelector('table');
        const rows = table.querySelectorAll('tbody tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            let rowText = '';

            cells.forEach(cell => {
                if (!cell.classList.contains('hidden-column')) {
                    rowText += cell.textContent.toLowerCase() + ' ';
                }
            });

            if (rowText.includes(searchTerm)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // Update search results indicator
        let indicator = input.parentNode.querySelector('.search-results');
        if (!indicator) {
            indicator = document.createElement('span');
            indicator.className = 'search-results text-sm text-gray-600 ml-2';
            input.parentNode.appendChild(indicator);
        }

        if (searchTerm) {
            indicator.textContent = `${visibleCount} de ${rows.length} registros`;
            indicator.style.display = 'inline';
        } else {
            indicator.style.display = 'none';
        }
    }

    function clearTableFilter(button) {
        const container = button.closest('.table-container');
        const input = container.querySelector('input[type="text"]');
        const table = container.querySelector('table');
        const rows = table.querySelectorAll('tbody tr');
        const indicator = container.querySelector('.search-results');

        input.value = '';
        rows.forEach(row => {
            row.style.display = '';
        });

        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    // Enhanced table sorting with visual indicators
    function sortTableColumn(columnIndex, headerElement) {
        const table = headerElement.closest('table');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        // Get current sort direction
        const currentDirection = headerElement.getAttribute('data-sort-direction') || 'none';
        let newDirection = 'asc';

        if (currentDirection === 'asc') {
            newDirection = 'desc';
        } else if (currentDirection === 'desc') {
            newDirection = 'asc';
        }

        // Clear all sort indicators
        table.querySelectorAll('th').forEach(th => {
            th.setAttribute('data-sort-direction', 'none');
        });

        // Set new sort indicator
        headerElement.setAttribute('data-sort-direction', newDirection);

        // Sort rows
        rows.sort((a, b) => {
            const aCell = a.cells[columnIndex];
            const bCell = b.cells[columnIndex];

            if (!aCell || !bCell) return 0;

            let aText = aCell.textContent.trim();
            let bText = bCell.textContent.trim();

            // Handle special cases
            if (aText === 'null' || aText === '') aText = '';
            if (bText === 'null' || bText === '') bText = '';

            // Try to parse as numbers
            const aNum = parseFloat(aText.replace(/[^\d.-]/g, ''));
            const bNum = parseFloat(bText.replace(/[^\d.-]/g, ''));

            let comparison = 0;

            if (!isNaN(aNum) && !isNaN(bNum)) {
                comparison = aNum - bNum;
            } else if (aText.match(/^\d{4}-\d{2}-\d{2}/) && bText.match(/^\d{4}-\d{2}-\d{2}/)) {
                // Date comparison
                comparison = new Date(aText) - new Date(bText);
            } else {
                // String comparison
                comparison = aText.localeCompare(bText, undefined, { numeric: true, sensitivity: 'base' });
            }

            return newDirection === 'desc' ? -comparison : comparison;
        });

        // Re-append sorted rows with animation
        rows.forEach((row, index) => {
            setTimeout(() => {
                tbody.appendChild(row);
            }, index * 10); // Stagger the animation
        });

        console.log(`Table sorted by column ${columnIndex} in ${newDirection} order`);
    }

    // Row selection functionality
    function selectAllTableRows(button) {
        const table = button.closest('.table-container').querySelector('table');
        const checkboxes = table.querySelectorAll('tbody input[type="checkbox"]');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(cb => {
            cb.checked = !allChecked;
        });

        updateSelectedCount(table);
        button.textContent = allChecked ? 'Seleccionar Todo' : 'Deseleccionar Todo';
    }

    function updateSelectedCount(table) {
        const checkboxes = table.querySelectorAll('tbody input[type="checkbox"]:checked');
        const counter = table.closest('.table-container').querySelector('#selected-count');
        if (counter) {
            counter.textContent = `${checkboxes.length} seleccionados`;
        }
    }

    // Pagination helper
    function showPaginationHelper(button) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Guía de Paginación OData</h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="space-y-4">
                    <div>
                        <h4 class="font-semibold text-blue-800 mb-2">Parámetros Básicos:</h4>
                        <div class="bg-gray-50 p-3 rounded">
                            <p><code class="bg-blue-100 px-2 py-1 rounded">$top=N</code> - Limita el número de registros a N</p>
                            <p><code class="bg-blue-100 px-2 py-1 rounded">$skip=N</code> - Omite los primeros N registros</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="font-semibold text-green-800 mb-2">Ejemplos Prácticos:</h4>
                        <div class="space-y-2 text-sm">
                            <div class="bg-green-50 p-2 rounded">
                                <strong>Primera página (20 registros):</strong><br>
                                <code>$top=20</code>
                            </div>
                            <div class="bg-green-50 p-2 rounded">
                                <strong>Segunda página (registros 21-40):</strong><br>
                                <code>$top=20&$skip=20</code>
                            </div>
                            <div class="bg-green-50 p-2 rounded">
                                <strong>Tercera página (registros 41-60):</strong><br>
                                <code>$top=20&$skip=40</code>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="font-semibold text-purple-800 mb-2">Combinando con Filtros:</h4>
                        <div class="bg-purple-50 p-3 rounded text-sm">
                            <p><code>$filter=Active eq 'Y'&$top=10&$skip=0</code></p>
                            <p class="text-purple-700 mt-1">Primeros 10 registros activos</p>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition">
                        Entendido
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    // Pagination helper functions
    function extractTopValue(params) {
        if (!params) return null;
        const match = params.match(/\$top=(\d+)/i);
        return match ? parseInt(match[1]) : null;
    }

    function extractSkipValue(params) {
        if (!params) return null;
        const match = params.match(/\$skip=(\d+)/i);
        return match ? parseInt(match[1]) : null;
    }

    function updateUrlParams(params, newTop, newSkip) {
        // Remove existing $top and $skip parameters
        let updatedParams = params.replace(/\$top=\d+/gi, '').replace(/\$skip=\d+/gi, '');

        // Clean up extra & characters
        updatedParams = updatedParams.replace(/&+/g, '&').replace(/^&|&$/g, '');

        // Add new parameters
        const newParams = [];
        if (newTop) newParams.push('$top=' + newTop);
        if (newSkip) newParams.push('$skip=' + newSkip);

        if (updatedParams && newParams.length > 0) {
            return updatedParams + '&' + newParams.join('&');
        } else if (newParams.length > 0) {
            return newParams.join('&');
        } else {
            return updatedParams;
        }
    }

    function navigateToPage(page, pageSize) {
        const currentForm = document.querySelector('#endpoint-test-form');
        if (!currentForm) return;

        const paramsInput = currentForm.querySelector('input[name="params"]');
        const currentParams = paramsInput.value || '';

        const newSkip = (page - 1) * pageSize;
        const newParams = updateUrlParams(currentParams, pageSize, newSkip);

        paramsInput.value = newParams;

        // Submit the form to reload data
        currentForm.dispatchEvent(new Event('submit'));

        console.log(`Navigating to page ${page} with ${pageSize} items per page`);
    }

    function changePaginationSize(selectElement) {
        const newSize = parseInt(selectElement.value);
        const currentForm = document.querySelector('#endpoint-test-form');
        if (!currentForm) return;

        const paramsInput = currentForm.querySelector('input[name="params"]');
        const currentParams = paramsInput.value || '';

        // Reset to first page when changing page size
        const newParams = updateUrlParams(currentParams, newSize, 0);
        paramsInput.value = newParams;

        // Submit the form to reload data
        currentForm.dispatchEvent(new Event('submit'));

        console.log(`Changed page size to ${newSize} items`);
    }

    // Enhanced URL validation and encoding
    function validateAndEncodeUrl(baseUrl, endpoint, params) {
        try {
            // Encode endpoint properly
            const encodedEndpoint = encodeURIComponent(endpoint).replace(/%2F/g, '/');
            let fullUrl = baseUrl + encodedEndpoint;

            if (params) {
                const encodedParams = encodeODataParams(params);
                fullUrl += '?' + encodedParams;
            }

            // Validate URL
            new URL(fullUrl);
            return fullUrl;
        } catch (error) {
            console.error('Invalid URL:', error);
            return null;
        }
    }

    // Initialize enhanced features
    document.addEventListener('DOMContentLoaded', function() {
        // Check session status every 5 minutes (skip initial check for periodic updates)
        setInterval(() => checkSessionStatus(true), 5 * 60 * 1000);

        // Initial session check - use PHP session status
        checkSessionStatus(false);

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+Enter to submit current form
            if (e.ctrlKey && e.key === 'Enter') {
                const activeForm = document.querySelector('form:focus-within');
                if (activeForm) {
                    activeForm.submit();
                }
            }

            // Ctrl+Shift+C to clear cache
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                if (confirm('Clear API response cache?')) {
                    makeEnhancedRequest(window.location.href, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'action=clear_cache'
                    })
                    .then(() => {
                        alert('Cache cleared successfully');
                    })
                    .catch(error => {
                        alert('Failed to clear cache: ' + error.message);
                    });
                }
            }
        });

        console.log('Enhanced SAP B1 API Explorer loaded successfully');

        // Complete page load performance tracking
        perfMonitor.end('page_load');

        // Add some sample metrics for demonstration
        perfMonitor.start('dom_ready');
        perfMonitor.end('dom_ready');

        // Log page load metrics
        const pageLoadMetric = perfMonitor.getMetrics()['page_load'];
        if (pageLoadMetric && pageLoadMetric.duration !== undefined) {
            console.log('Page Load Performance:', {
                duration: pageLoadMetric.duration.toFixed(2) + 'ms',
                memory_used: pageLoadMetric.memoryDelta ? (pageLoadMetric.memoryDelta / 1024).toFixed(2) + 'KB' : 'N/A'
            });
        }
    });

    // Add global error handler
    window.addEventListener('error', function(e) {
        console.error('Global error:', e.error);
        logMessage('ERROR', 'JavaScript error', {
            message: e.message,
            filename: e.filename,
            lineno: e.lineno,
            colno: e.colno
        });
    });

    // Add unhandled promise rejection handler
    window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled promise rejection:', e.reason);
        logMessage('ERROR', 'Unhandled promise rejection', {
            reason: e.reason.toString()
        });
    });
    </script>

    <?php
    // End page load performance tracking
    PerformanceTracker::end('page_load');

    // Log final performance metrics
    $final_time = microtime(true);
    $final_memory = memory_get_usage();
    $total_time = $final_time - $start_time;
    $total_memory = $final_memory - $memory_start;

    logMessage('INFO', 'Page load completed', [
        'total_time' => round($total_time, 3) . 's',
        'total_memory' => round($total_memory / 1024, 2) . 'KB',
        'peak_memory' => round(memory_get_peak_usage() / 1024, 2) . 'KB',
        'auto_connect_result' => $auto_connect_result ? 'success' : 'failed'
    ]);
    ?>
</body>
</html>
