{"data": {"success": true, "http_code": 200, "data": {"@odata.context": "https://*************:50000/b1s/v2/$metadata#UserTablesMD", "value": [{"TableName": "EPY_PLPY", "TableDescription": "General", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_DEMP", "TableDescription": "Detalle Empresas", "TableType": "bott_MasterDataLines", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_DSUC", "TableDescription": "Detalle de Sucursales", "TableType": "bott_MasterDataLines", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_TR", "TableDescription": "Tarjeta Retenciones", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_MRET", "TableDescription": "Maestro de Retenciones", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_RSIT", "TableDescription": "Maestro de situación", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_RIDT", "TableDescription": "M. identificación Proveedor", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_RTIC", "TableDescription": "<PERSON><PERSON> Comproban<PERSON>", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_RCRT", "TableDescription": "M. Cptos. R. por Situación", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_RCRI", "TableDescription": "M. Conceptos IVA (Retención)", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_DRDI", "TableDescription": "Retención de Impuestos", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_CRAP", "TableDescription": "Lista certificado no retención", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_DCR", "TableDescription": "Lista cert. no retención d.", "TableType": "bott_MasterDataLines", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_ARIR", "TableDescription": "Art Retención IVA y RENTA", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_HCOM", "TableDescription": "<PERSON><PERSON>", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_HTOP", "TableDescription": "Maestro de Tipo de Operaciones", "TableType": "bott_MasterDataLines", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_HDOC", "TableDescription": "Maestro de Documentos", "TableType": "bott_MasterDataLines", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_ABS_H", "TableDescription": "Absorciones Retenc. Ext. (H)", "TableType": "bott_Document", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_ABS_L", "TableDescription": "Absorciones Reten. Ext. (L)", "TableType": "bott_DocumentLines", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}, {"TableName": "EPY_HVTA", "TableDescription": "<PERSON><PERSON>", "TableType": "bott_MasterData", "Archivable": "tNO", "ArchiveDateField": null, "DisplayMenu": "tYES", "ApplyAuthorization": "tNO"}]}, "raw_response": "{\n\t\"@odata.context\" : \"https://*************:50000/b1s/v2/$metadata#UserTablesMD\",\n\t\"value\" : \n\t[\n\t\t{\n\t\t\t\"TableName\" : \"EPY_PLPY\",\n\t\t\t\"TableDescription\" : \"General\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_DEMP\",\n\t\t\t\"TableDescription\" : \"Detalle Empresas\",\n\t\t\t\"TableType\" : \"bott_MasterDataLines\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_DSUC\",\n\t\t\t\"TableDescription\" : \"Detalle de Sucursales\",\n\t\t\t\"TableType\" : \"bott_MasterDataLines\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_TR\",\n\t\t\t\"TableDescription\" : \"Tarjeta Retenciones\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_MRET\",\n\t\t\t\"TableDescription\" : \"Maestro de Retenciones\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_RSIT\",\n\t\t\t\"TableDescription\" : \"Maestro de situación\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_RIDT\",\n\t\t\t\"TableDescription\" : \"M. identificación Proveedor\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_RTIC\",\n\t\t\t\"TableDescription\" : \"M. Tipos Comprobantes\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_RCRT\",\n\t\t\t\"TableDescription\" : \"M. Cptos. R. por Situación\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_RCRI\",\n\t\t\t\"TableDescription\" : \"M. Conceptos IVA (Retención)\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_DRDI\",\n\t\t\t\"TableDescription\" : \"Retención de Impuestos\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_CRAP\",\n\t\t\t\"TableDescription\" : \"Lista certificado no retención\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_DCR\",\n\t\t\t\"TableDescription\" : \"Lista cert. no retención d.\",\n\t\t\t\"TableType\" : \"bott_MasterDataLines\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_ARIR\",\n\t\t\t\"TableDescription\" : \"Art Retención IVA y RENTA\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_HCOM\",\n\t\t\t\"TableDescription\" : \"Datos Hechauka Compras\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_HTOP\",\n\t\t\t\"TableDescription\" : \"Maestro de Tipo de Operaciones\",\n\t\t\t\"TableType\" : \"bott_MasterDataLines\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_HDOC\",\n\t\t\t\"TableDescription\" : \"Maestro de Documentos\",\n\t\t\t\"TableType\" : \"bott_MasterDataLines\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_ABS_H\",\n\t\t\t\"TableDescription\" : \"Absorciones Retenc. Ext. (H)\",\n\t\t\t\"TableType\" : \"bott_Document\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_ABS_L\",\n\t\t\t\"TableDescription\" : \"Absorciones Reten. Ext. (L)\",\n\t\t\t\"TableType\" : \"bott_DocumentLines\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"TableName\" : \"EPY_HVTA\",\n\t\t\t\"TableDescription\" : \"Datos Hechauka Ventas\",\n\t\t\t\"TableType\" : \"bott_MasterData\",\n\t\t\t\"Archivable\" : \"tNO\",\n\t\t\t\"ArchiveDateField\" : null,\n\t\t\t\"DisplayMenu\" : \"tYES\",\n\t\t\t\"ApplyAuthorization\" : \"tNO\"\n\t\t}\n\t]\n}", "info": {"url": "https://*************:50000/b1s/v2/UserTablesMD?%24skip=0&%24top=20", "content_type": "application/json;odata.metadata=minimal;charset=utf-8", "http_code": 200, "header_size": 299, "request_size": 315, "filetime": -1, "ssl_verify_result": 18, "redirect_count": 0, "total_time": 0.077787, "namelookup_time": 0.000885, "connect_time": 0.028435, "pretransfer_time": 0.050847, "size_upload": 0, "size_download": 4914, "speed_download": 63172, "speed_upload": 0, "download_content_length": -1, "upload_content_length": 0, "starttransfer_time": 0.077727, "redirect_time": 0, "redirect_url": "", "primary_ip": "*************", "certinfo": [], "primary_port": 50000, "local_ip": "**************", "local_port": 52604, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "https", "appconnect_time_us": 50522, "connect_time_us": 28435, "namelookup_time_us": 885, "pretransfer_time_us": 50847, "redirect_time_us": 0, "starttransfer_time_us": 77727, "posttransfer_time_us": 50943, "total_time_us": 77787, "effective_method": "GET", "capath": "", "cainfo": ""}, "request_id": "req_68635bc687ca4", "request_time": 0.07801413536071777, "cached": false}, "expires": 1751342324, "created": 1751342024}