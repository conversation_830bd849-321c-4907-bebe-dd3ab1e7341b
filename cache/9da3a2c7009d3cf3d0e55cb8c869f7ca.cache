{"data": {"success": true, "http_code": 200, "data": {"@odata.context": "https://*************:50000/b1s/v2/$metadata#SalesPersons", "value": [{"SalesEmployeeCode": -1, "SalesEmployeeName": "-Ningún empleado del departamento de ventas-", "Remarks": null, "CommissionForSalesEmployee": 0, "CommissionGroup": 0, "Locked": "tYES", "EmployeeID": null, "Active": "tYES", "Telephone": null, "Mobile": null, "Fax": null, "Email": null}, {"SalesEmployeeCode": 1, "SalesEmployeeName": "DONACION", "Remarks": null, "CommissionForSalesEmployee": 0, "CommissionGroup": 0, "Locked": "tNO", "EmployeeID": null, "Active": "tYES", "Telephone": null, "Mobile": null, "Fax": null, "Email": null}]}, "raw_response": "{\n\t\"@odata.context\" : \"https://*************:50000/b1s/v2/$metadata#SalesPersons\",\n\t\"value\" : \n\t[\n\t\t{\n\t\t\t\"SalesEmployeeCode\" : -1,\n\t\t\t\"SalesEmployeeName\" : \"-Ningún empleado del departamento de ventas-\",\n\t\t\t\"Remarks\" : null,\n\t\t\t\"CommissionForSalesEmployee\" : 0.0,\n\t\t\t\"CommissionGroup\" : 0,\n\t\t\t\"Locked\" : \"tYES\",\n\t\t\t\"EmployeeID\" : null,\n\t\t\t\"Active\" : \"tYES\",\n\t\t\t\"Telephone\" : null,\n\t\t\t\"Mobile\" : null,\n\t\t\t\"Fax\" : null,\n\t\t\t\"Email\" : null\n\t\t},\n\t\t{\n\t\t\t\"SalesEmployeeCode\" : 1,\n\t\t\t\"SalesEmployeeName\" : \"DONACION\",\n\t\t\t\"Remarks\" : null,\n\t\t\t\"CommissionForSalesEmployee\" : 0.0,\n\t\t\t\"CommissionGroup\" : 0,\n\t\t\t\"Locked\" : \"tNO\",\n\t\t\t\"EmployeeID\" : null,\n\t\t\t\"Active\" : \"tYES\",\n\t\t\t\"Telephone\" : null,\n\t\t\t\"Mobile\" : null,\n\t\t\t\"Fax\" : null,\n\t\t\t\"Email\" : null\n\t\t}\n\t]\n}", "info": {"url": "https://*************:50000/b1s/v2/SalesPersons?%24skip=0&%24top=20", "content_type": "application/json;odata.metadata=minimal;charset=utf-8", "http_code": 200, "header_size": 299, "request_size": 315, "filetime": -1, "ssl_verify_result": 18, "redirect_count": 0, "total_time": 0.088104, "namelookup_time": 0.000874, "connect_time": 0.025807, "pretransfer_time": 0.048639, "size_upload": 0, "size_download": 750, "speed_download": 8512, "speed_upload": 0, "download_content_length": -1, "upload_content_length": 0, "starttransfer_time": 0.088025, "redirect_time": 0, "redirect_url": "", "primary_ip": "*************", "certinfo": [], "primary_port": 50000, "local_ip": "**************", "local_port": 51356, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "https", "appconnect_time_us": 48457, "connect_time_us": 25807, "namelookup_time_us": 874, "pretransfer_time_us": 48639, "redirect_time_us": 0, "starttransfer_time_us": 88025, "posttransfer_time_us": 48690, "total_time_us": 88104, "effective_method": "GET", "capath": "", "cainfo": ""}, "request_id": "req_686358e4729de", "request_time": 0.08830094337463379, "cached": false}, "expires": 1751341586, "created": 1751341286}