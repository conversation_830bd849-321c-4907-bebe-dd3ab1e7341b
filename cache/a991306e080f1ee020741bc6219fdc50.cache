{"data": {"success": true, "http_code": 200, "data": {"@odata.context": "https://*************:50000/b1s/v2/$metadata#EPY_RCFG", "value": [{"Code": "01", "Name": "01", "DocEntry": 1, "Canceled": "N", "Object": "EPY_RCFG", "LogInst": null, "UserSign": 1, "Transfered": "N", "CreateDate": "2023-10-12T00:00:00Z", "CreateTime": "11:36:00", "UpdateDate": "2025-02-19T00:00:00Z", "UpdateTime": "16:10:00", "DataSource": "O", "EPY_REGRALCollection": [{"Code": "01", "LineId": 1, "Object": "EPY_RCFG", "LogInst": null, "U_CREGI": "2", "U_FECHIN": null, "U_INCT": "N", "U_NEMP": "DE generado en ambiente de prueba - sin valor comercial ni fiscal", "U_NREGI": "Importador", "U_CEMP": "1"}]}]}, "raw_response": "{\n\t\"@odata.context\" : \"https://*************:50000/b1s/v2/$metadata#EPY_RCFG\",\n\t\"value\" : \n\t[\n\t\t{\n\t\t\t\"Code\" : \"01\",\n\t\t\t\"Name\" : \"01\",\n\t\t\t\"DocEntry\" : 1,\n\t\t\t\"Canceled\" : \"N\",\n\t\t\t\"Object\" : \"EPY_RCFG\",\n\t\t\t\"LogInst\" : null,\n\t\t\t\"UserSign\" : 1,\n\t\t\t\"Transfered\" : \"N\",\n\t\t\t\"CreateDate\" : \"2023-10-12T00:00:00Z\",\n\t\t\t\"CreateTime\" : \"11:36:00\",\n\t\t\t\"UpdateDate\" : \"2025-02-19T00:00:00Z\",\n\t\t\t\"UpdateTime\" : \"16:10:00\",\n\t\t\t\"DataSource\" : \"O\",\n\t\t\t\"EPY_REGRALCollection\" : \n\t\t\t[\n\t\t\t\t{\n\t\t\t\t\t\"Code\" : \"01\",\n\t\t\t\t\t\"LineId\" : 1,\n\t\t\t\t\t\"Object\" : \"EPY_RCFG\",\n\t\t\t\t\t\"LogInst\" : null,\n\t\t\t\t\t\"U_CREGI\" : \"2\",\n\t\t\t\t\t\"U_FECHIN\" : null,\n\t\t\t\t\t\"U_INCT\" : \"N\",\n\t\t\t\t\t\"U_NEMP\" : \"DE generado en ambiente de prueba - sin valor comercial ni fiscal\",\n\t\t\t\t\t\"U_NREGI\" : \"Importador\",\n\t\t\t\t\t\"U_CEMP\" : \"1\"\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t]\n}", "info": {"url": "https://*************:50000/b1s/v2/EPY_RCFG?%24skip=0&%24top=20", "content_type": "application/json;odata.metadata=minimal;charset=utf-8", "http_code": 200, "header_size": 299, "request_size": 311, "filetime": -1, "ssl_verify_result": 18, "redirect_count": 0, "total_time": 0.090266, "namelookup_time": 0.000715, "connect_time": 0.012562, "pretransfer_time": 0.033269, "size_upload": 0, "size_download": 782, "speed_download": 8663, "speed_upload": 0, "download_content_length": -1, "upload_content_length": 0, "starttransfer_time": 0.090206, "redirect_time": 0, "redirect_url": "", "primary_ip": "*************", "certinfo": [], "primary_port": 50000, "local_ip": "**************", "local_port": 51520, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "https", "appconnect_time_us": 33168, "connect_time_us": 12562, "namelookup_time_us": 715, "pretransfer_time_us": 33269, "redirect_time_us": 0, "starttransfer_time_us": 90206, "posttransfer_time_us": 33376, "total_time_us": 90266, "effective_method": "GET", "capath": "", "cainfo": ""}, "request_id": "req_686359497bfa4", "request_time": 0.09052515029907227, "cached": false}, "expires": 1751341687, "created": 1751341387}