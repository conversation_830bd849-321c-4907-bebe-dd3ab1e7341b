<?php
/**
 * Documents Controller
 * Handles sales orders, purchase orders, and other documents
 */
class DocumentsController {
    private $client;

    public function __construct() {
        $this->client = new SAPClient();
    }

    /**
     * Get recent documents
     */
    public function getRecentDocuments() {
        try {
            // For now, return mock data since we don't have a real SAP connection
            // In production, this would query the SAP B1 Service Layer
            
            $recentDocuments = [
                [
                    'docEntry' => 1001,
                    'docNum' => 'SO-001',
                    'docType' => 'Sales Order',
                    'cardCode' => 'C001',
                    'cardName' => 'ABC Corporation',
                    'docDate' => date('Y-m-d'),
                    'docTotal' => 15750.00,
                    'docStatus' => 'Open'
                ],
                [
                    'docEntry' => 1002,
                    'docNum' => 'PO-001',
                    'docType' => 'Purchase Order',
                    'cardCode' => 'V001',
                    'cardName' => 'XYZ Suppliers',
                    'docDate' => date('Y-m-d', strtotime('-1 day')),
                    'docTotal' => 8500.00,
                    'docStatus' => 'Open'
                ],
                [
                    'docEntry' => 1003,
                    'docNum' => 'INV-001',
                    'docType' => 'A/R Invoice',
                    'cardCode' => 'C002',
                    'cardName' => 'DEF Industries',
                    'docDate' => date('Y-m-d', strtotime('-2 days')),
                    'docTotal' => 12300.00,
                    'docStatus' => 'Closed'
                ]
            ];

            return [
                'success' => true,
                'data' => $recentDocuments,
                'message' => 'Recent documents retrieved successfully'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to retrieve recent documents: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get sales orders
     */
    public function getSalesOrders($params = []) {
        try {
            // Mock data for sales orders
            $salesOrders = [
                [
                    'docEntry' => 1001,
                    'docNum' => 'SO-001',
                    'cardCode' => 'C001',
                    'cardName' => 'ABC Corporation',
                    'docDate' => date('Y-m-d'),
                    'docDueDate' => date('Y-m-d', strtotime('+30 days')),
                    'docTotal' => 15750.00,
                    'docStatus' => 'Open'
                ],
                [
                    'docEntry' => 1004,
                    'docNum' => 'SO-002',
                    'cardCode' => 'C003',
                    'cardName' => 'GHI Company',
                    'docDate' => date('Y-m-d', strtotime('-1 day')),
                    'docDueDate' => date('Y-m-d', strtotime('+29 days')),
                    'docTotal' => 22100.00,
                    'docStatus' => 'Open'
                ]
            ];

            return [
                'success' => true,
                'data' => $salesOrders,
                'message' => 'Sales orders retrieved successfully'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to retrieve sales orders: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get single sales order
     */
    public function getSalesOrder($docEntry) {
        try {
            // Mock data for single sales order
            $salesOrder = [
                'docEntry' => $docEntry,
                'docNum' => 'SO-001',
                'cardCode' => 'C001',
                'cardName' => 'ABC Corporation',
                'docDate' => date('Y-m-d'),
                'docDueDate' => date('Y-m-d', strtotime('+30 days')),
                'docTotal' => 15750.00,
                'docStatus' => 'Open',
                'documentLines' => [
                    [
                        'lineNum' => 0,
                        'itemCode' => 'ITEM001',
                        'itemDescription' => 'Sample Item 1',
                        'quantity' => 10,
                        'price' => 750.00,
                        'lineTotal' => 7500.00
                    ],
                    [
                        'lineNum' => 1,
                        'itemCode' => 'ITEM002',
                        'itemDescription' => 'Sample Item 2',
                        'quantity' => 5,
                        'price' => 1650.00,
                        'lineTotal' => 8250.00
                    ]
                ]
            ];

            return [
                'success' => true,
                'data' => $salesOrder,
                'message' => 'Sales order retrieved successfully'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to retrieve sales order: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create sales order
     */
    public function createSalesOrder($data) {
        try {
            // In production, this would create a sales order in SAP B1
            return [
                'success' => true,
                'data' => [
                    'docEntry' => rand(1000, 9999),
                    'docNum' => 'SO-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT)
                ],
                'message' => 'Sales order created successfully'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to create sales order: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get purchase orders
     */
    public function getPurchaseOrders($params = []) {
        try {
            // Mock data for purchase orders
            $purchaseOrders = [
                [
                    'docEntry' => 2001,
                    'docNum' => 'PO-001',
                    'cardCode' => 'V001',
                    'cardName' => 'XYZ Suppliers',
                    'docDate' => date('Y-m-d'),
                    'docDueDate' => date('Y-m-d', strtotime('+15 days')),
                    'docTotal' => 8500.00,
                    'docStatus' => 'Open'
                ]
            ];

            return [
                'success' => true,
                'data' => $purchaseOrders,
                'message' => 'Purchase orders retrieved successfully'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to retrieve purchase orders: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create purchase order
     */
    public function createPurchaseOrder($data) {
        try {
            // In production, this would create a purchase order in SAP B1
            return [
                'success' => true,
                'data' => [
                    'docEntry' => rand(2000, 2999),
                    'docNum' => 'PO-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT)
                ],
                'message' => 'Purchase order created successfully'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to create purchase order: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get GL Accounts
     */
    public function getGLAccounts($params = []) {
        try {
            // Mock data for GL accounts
            $glAccounts = [
                [
                    'acctCode' => '1100',
                    'acctName' => 'Cash',
                    'groupCode' => 1,
                    'balance' => 50000.00
                ],
                [
                    'acctCode' => '1200',
                    'acctName' => 'Accounts Receivable',
                    'groupCode' => 2,
                    'balance' => 75000.00
                ]
            ];

            return [
                'success' => true,
                'data' => $glAccounts,
                'message' => 'GL accounts retrieved successfully'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to retrieve GL accounts: ' . $e->getMessage()
            ];
        }
    }
}
?>
