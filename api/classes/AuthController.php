<?php
/**
 * Authentication Controller
 * Handles login, logout, and session management
 */
class AuthController {
    private $client;

    public function __construct() {
        $this->client = new SAPClient();
    }

    /**
     * Handle login request
     */
    public function login($data) {
        try {
            if (!$data || !isset($data['database']) || !isset($data['username']) || !isset($data['password'])) {
                return [
                    'success' => false,
                    'message' => 'Missing required fields: database, username, password'
                ];
            }

            $result = $this->client->login($data['database'], $data['username'], $data['password']);
            
            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'Login successful',
                    'data' => [
                        'sessionId' => $result['sessionId'],
                        'database' => $data['database'],
                        'username' => $data['username']
                    ]
                ];
            }

            return $result;

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Login failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Handle logout request
     */
    public function logout() {
        try {
            $result = $this->client->logout();
            return $result;

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Logout failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Refresh authentication token
     */
    public function refreshToken() {
        try {
            $sessionInfo = $this->client->getSessionInfo();
            
            if ($sessionInfo['authenticated']) {
                return [
                    'success' => true,
                    'message' => 'Token is valid',
                    'data' => $sessionInfo
                ];
            }

            return [
                'success' => false,
                'message' => 'Token expired or invalid'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Token refresh failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check if user is authenticated
     */
    public function checkAuth() {
        try {
            $sessionInfo = $this->client->getSessionInfo();
            return [
                'success' => true,
                'data' => $sessionInfo
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Auth check failed: ' . $e->getMessage()
            ];
        }
    }
}
?>
