<?php
/**
 * SAP Business One Service Layer Client
 * Handles communication with SAP B1 Service Layer
 */
class SAPClient {
    private $baseUrl;
    private $sessionId;
    private $database;
    private $username;
    private $password;

    public function __construct() {
        $this->baseUrl = SAP_BASE_URL;
        $this->loadSession();
    }

    /**
     * Login to SAP B1 Service Layer
     */
    public function login($database, $username, $password) {
        $this->database = $database;
        $this->username = $username;
        $this->password = $password;

        $loginData = [
            'CompanyDB' => $database,
            'UserName' => $username,
            'Password' => $password
        ];

        $response = $this->makeRequest('POST', 'Login', $loginData);
        
        if ($response && isset($response['SessionId'])) {
            $this->sessionId = $response['SessionId'];
            $this->saveSession();
            return [
                'success' => true,
                'sessionId' => $this->sessionId,
                'message' => 'Login successful'
            ];
        }

        return [
            'success' => false,
            'message' => 'Login failed'
        ];
    }

    /**
     * Logout from SAP B1 Service Layer
     */
    public function logout() {
        if ($this->sessionId) {
            $this->makeRequest('POST', 'Logout');
            $this->clearSession();
        }
        
        return [
            'success' => true,
            'message' => 'Logout successful'
        ];
    }

    /**
     * Make a GET request
     */
    public function get($endpoint, $params = []) {
        $url = $endpoint;
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $this->makeRequest('GET', $url);
    }

    /**
     * Make a POST request
     */
    public function post($endpoint, $data = []) {
        return $this->makeRequest('POST', $endpoint, $data);
    }

    /**
     * Make a PUT request
     */
    public function put($endpoint, $data = []) {
        return $this->makeRequest('PUT', $endpoint, $data);
    }

    /**
     * Make a DELETE request
     */
    public function delete($endpoint) {
        return $this->makeRequest('DELETE', $endpoint);
    }

    /**
     * Make HTTP request to SAP B1 Service Layer
     */
    private function makeRequest($method, $endpoint, $data = null) {
        $url = $this->baseUrl . '/' . ltrim($endpoint, '/');
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, SAP_SSL_VERIFY);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        if ($this->sessionId && $endpoint !== 'Login') {
            $headers[] = 'Cookie: B1SESSION=' . $this->sessionId;
        }
        
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception("cURL Error: " . $error);
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $errorMessage = 'HTTP Error ' . $httpCode;
            if ($decodedResponse && isset($decodedResponse['error'])) {
                $errorMessage = $decodedResponse['error']['message']['value'] ?? $errorMessage;
            }
            throw new Exception($errorMessage);
        }
        
        return $decodedResponse;
    }

    /**
     * Save session to storage
     */
    private function saveSession() {
        if (!$this->sessionId) return;
        
        $sessionData = [
            'sessionId' => $this->sessionId,
            'database' => $this->database,
            'username' => $this->username,
            'timestamp' => time()
        ];
        
        // In production, use Redis or database storage
        $sessionFile = sys_get_temp_dir() . '/sap_session_' . session_id() . '.json';
        file_put_contents($sessionFile, json_encode($sessionData));
    }

    /**
     * Load session from storage
     */
    private function loadSession() {
        $sessionFile = sys_get_temp_dir() . '/sap_session_' . session_id() . '.json';
        
        if (file_exists($sessionFile)) {
            $sessionData = json_decode(file_get_contents($sessionFile), true);
            
            if ($sessionData && isset($sessionData['sessionId'])) {
                // Check if session is not expired (24 hours)
                if (time() - $sessionData['timestamp'] < 86400) {
                    $this->sessionId = $sessionData['sessionId'];
                    $this->database = $sessionData['database'];
                    $this->username = $sessionData['username'];
                }
            }
        }
    }

    /**
     * Clear session from storage
     */
    private function clearSession() {
        $this->sessionId = null;
        $this->database = null;
        $this->username = null;
        
        $sessionFile = sys_get_temp_dir() . '/sap_session_' . session_id() . '.json';
        if (file_exists($sessionFile)) {
            unlink($sessionFile);
        }
    }

    /**
     * Check if user is authenticated
     */
    public function isAuthenticated() {
        return !empty($this->sessionId);
    }

    /**
     * Get current session info
     */
    public function getSessionInfo() {
        return [
            'authenticated' => $this->isAuthenticated(),
            'database' => $this->database,
            'username' => $this->username
        ];
    }
}
?>
