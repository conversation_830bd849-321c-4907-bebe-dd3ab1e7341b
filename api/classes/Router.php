<?php
/**
 * Simple Router Class
 * Handles routing for the SAP B1 API
 */
class Router {
    private $routes = [];

    /**
     * Add a route
     */
    public function addRoute($method, $pattern, $callback) {
        $this->routes[] = [
            'method' => $method,
            'pattern' => $pattern,
            'callback' => $callback
        ];
    }

    /**
     * Route a request
     */
    public function route($method, $path) {
        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }

            // Convert pattern to regex
            $pattern = '#^' . $route['pattern'] . '$#';
            
            if (preg_match($pattern, $path, $matches)) {
                // Remove the full match
                array_shift($matches);
                
                // Call the callback with matches as parameters
                return call_user_func_array($route['callback'], $matches);
            }
        }

        return false; // No route found
    }
}
?>
