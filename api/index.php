<?php
/**
 * SAP Business One Web Client API Router
 * Routes requests to appropriate SAP B1 Service Layer endpoints
 */

// Start session
session_start();

// Set headers for CORS and JSON response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in production

// Load configuration and classes
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/classes/SAPClient.php';
require_once __DIR__ . '/classes/Router.php';
require_once __DIR__ . '/classes/AuthController.php';
require_once __DIR__ . '/classes/BusinessPartnersController.php';
require_once __DIR__ . '/classes/ItemsController.php';
require_once __DIR__ . '/classes/DocumentsController.php';

// Initialize router
$router = new Router();

// Get request information
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/api', '', $path); // Remove /api prefix
$path = trim($path, '/');

// Get request body for POST/PUT/PATCH requests
$input = null;
if (in_array($method, ['POST', 'PUT', 'PATCH'])) {
    $input = json_decode(file_get_contents('php://input'), true);
}

// Authentication routes
$router->addRoute('POST', 'login', function() use ($input) {
    $controller = new AuthController();
    return $controller->login($input);
});

$router->addRoute('POST', 'logout', function() {
    $controller = new AuthController();
    return $controller->logout();
});

$router->addRoute('POST', 'refresh-token', function() {
    $controller = new AuthController();
    return $controller->refreshToken();
});

// Business Partners routes
$router->addRoute('GET', 'businesspartners', function() {
    $controller = new BusinessPartnersController();
    return $controller->getBusinessPartners($_GET);
});

$router->addRoute('GET', 'businesspartners/([a-zA-Z0-9_-]+)', function($cardCode) {
    $controller = new BusinessPartnersController();
    return $controller->getBusinessPartner($cardCode);
});

$router->addRoute('POST', 'businesspartners', function() use ($input) {
    $controller = new BusinessPartnersController();
    return $controller->createBusinessPartner($input);
});

$router->addRoute('PUT', 'businesspartners/([a-zA-Z0-9_-]+)', function($cardCode) use ($input) {
    $controller = new BusinessPartnersController();
    return $controller->updateBusinessPartner($cardCode, $input);
});

// Items routes
$router->addRoute('GET', 'items', function() {
    $controller = new ItemsController();
    return $controller->getItems($_GET);
});

$router->addRoute('GET', 'items/([a-zA-Z0-9_-]+)', function($itemCode) {
    $controller = new ItemsController();
    return $controller->getItem($itemCode);
});

$router->addRoute('POST', 'items', function() use ($input) {
    $controller = new ItemsController();
    return $controller->createItem($input);
});

// Documents routes
$router->addRoute('GET', 'orders', function() {
    $controller = new DocumentsController();
    return $controller->getSalesOrders($_GET);
});

$router->addRoute('GET', 'orders/([0-9]+)', function($docEntry) {
    $controller = new DocumentsController();
    return $controller->getSalesOrder($docEntry);
});

$router->addRoute('POST', 'orders', function() use ($input) {
    $controller = new DocumentsController();
    return $controller->createSalesOrder($input);
});

$router->addRoute('GET', 'purchaseorders', function() {
    $controller = new DocumentsController();
    return $controller->getPurchaseOrders($_GET);
});

$router->addRoute('POST', 'purchaseorders', function() use ($input) {
    $controller = new DocumentsController();
    return $controller->createPurchaseOrder($input);
});

// Utility routes
$router->addRoute('GET', 'warehouses', function() {
    $controller = new ItemsController();
    return $controller->getWarehouses();
});

$router->addRoute('GET', 'glaccounts', function() {
    $controller = new DocumentsController();
    return $controller->getGLAccounts($_GET);
});

$router->addRoute('GET', 'userobjectsmd', function() {
    $client = new SAPClient();
    return $client->get('UserObjectsMD');
});

$router->addRoute('GET', 'documents/recent', function() {
    $controller = new DocumentsController();
    return $controller->getRecentDocuments();
});

// Reports routes
$router->addRoute('GET', 'reports/([a-zA-Z0-9_-]+)', function($reportName) {
    // Placeholder for reports functionality
    return [
        'success' => true,
        'data' => [],
        'message' => "Report '{$reportName}' not yet implemented"
    ];
});

// Error handling
try {
    // Route the request
    $result = $router->route($method, $path);
    
    if ($result === false) {
        // Route not found
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Endpoint not found',
            'message' => "No route found for {$method} {$path}"
        ]);
    } else {
        // Success response
        if (isset($result['http_code'])) {
            http_response_code($result['http_code']);
        } else {
            http_response_code($result['success'] ? 200 : 400);
        }
        echo json_encode($result);
    }
} catch (Exception $e) {
    // Handle exceptions
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => 'An unexpected error occurred',
        'debug' => DEBUG_MODE ? $e->getMessage() : null
    ]);
    
    // Log error
    error_log("API Error: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
}
?>
