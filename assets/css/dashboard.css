/* Dashboard Enhanced Styles - SAP Business One Theme */

/* SAP Business One Color Palette */
:root {
    --sap-primary: #0078d4;
    --sap-primary-dark: #106ebe;
    --sap-secondary: #605e5c;
    --sap-accent: #0078d4;
    --sap-success: #107c10;
    --sap-warning: #ff8c00;
    --sap-error: #d13438;
    --sap-background: #faf9f8;
    --sap-surface: #ffffff;
    --sap-border: #edebe9;
    --sap-text-primary: #323130;
    --sap-text-secondary: #605e5c;
    --sap-shadow: rgba(0, 0, 0, 0.133);
}

/* Dashboard Container */
.dashboard {
    padding: 1.5rem;
    background: var(--sap-background);
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--sap-surface);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--sap-shadow);
    border-left: 4px solid var(--sap-primary);
}

.dashboard-header h1 {
    margin: 0;
    color: var(--sap-text-primary);
    font-size: 1.75rem;
    font-weight: 600;
}

.dashboard-controls {
    display: flex;
    gap: 0.75rem;
}

/* Dashboard Widgets */
.dashboard-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.widget {
    background: var(--sap-surface);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--sap-shadow);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid var(--sap-border);
}

.widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px var(--sap-shadow);
}

.widget-header {
    background: var(--sap-primary);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.widget-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.widget-controls select {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.widget-content {
    padding: 1.5rem;
}

/* Quick Actions Grid */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.quick-btn {
    background: var(--sap-primary);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
    font-size: 0.9rem;
    font-weight: 500;
}

.quick-btn:hover {
    background: var(--sap-primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--sap-shadow);
}

.quick-btn i {
    font-size: 1.5rem;
}

/* System Status Grid */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.status-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #667eea;
    color: white;
    font-size: 1.2rem;
}

.status-icon.connected {
    background: #28a745;
}

.status-info {
    flex: 1;
}

.status-label {
    display: block;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.status-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.status-value.connected {
    color: #28a745;
}

.status-value.disconnected {
    color: #dc3545;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.metric-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.metric-card:hover {
    background: #e9ecef;
}

.metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.metric-icon.sales {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-icon.orders {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metric-icon.inventory {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-icon.customers {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metric-info {
    flex: 1;
}

.metric-label {
    display: block;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.metric-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.metric-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.metric-change.positive {
    color: #28a745;
}

.metric-change.negative {
    color: #dc3545;
}

.metric-change.warning {
    color: #ffc107;
}

/* Dashboard Cards */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

.card-header i {
    font-size: 1.5rem;
    opacity: 0.8;
}

.card-content {
    padding: 1.5rem;
    text-align: center;
}

.metric-large {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.metric-actions {
    margin-top: 1rem;
}

/* Status Badges */
.status-badge, .priority-badge, .stage-badge, .action-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active, .status-badge.connected {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive, .status-badge.disconnected {
    background: #f8d7da;
    color: #721c24;
}

.priority-badge.low {
    background: #d1ecf1;
    color: #0c5460;
}

.priority-badge.medium {
    background: #fff3cd;
    color: #856404;
}

.priority-badge.high {
    background: #f8d7da;
    color: #721c24;
}

.priority-badge.urgent {
    background: #721c24;
    color: white;
}

/* Progress Bars */
.progress-bar {
    position: relative;
    background: #e9ecef;
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Fullscreen Dashboard */
.dashboard.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background: white;
    padding: 2rem;
    overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-widgets {
        grid-template-columns: 1fr;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .status-grid, .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .dashboard-controls {
        justify-content: center;
    }
}
