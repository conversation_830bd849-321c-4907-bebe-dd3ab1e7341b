/* SAP Business One Color Palette */
:root {
    --sap-primary: #0078d4;
    --sap-primary-dark: #106ebe;
    --sap-secondary: #605e5c;
    --sap-accent: #0078d4;
    --sap-success: #107c10;
    --sap-warning: #ff8c00;
    --sap-error: #d13438;
    --sap-background: #faf9f8;
    --sap-surface: #ffffff;
    --sap-border: #edebe9;
    --sap-text-primary: #323130;
    --sap-text-secondary: #605e5c;
    --sap-shadow: rgba(0, 0, 0, 0.133);
}

/* Main Application Layout */
.main-app {
    display: none;
    height: 100vh;
    flex-direction: column;
}

.main-app.active {
    display: flex;
}

/* Header */
.app-header {
    background: var(--sap-primary);
    color: white;
    padding: 0 20px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px var(--sap-shadow);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-logo {
    width: 60px;
    height: 30px;
    background: #0078d4;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.header-logo::before {
    content: "SAP";
}

.company-name {
    font-size: 18px;
    font-weight: 500;
}

.header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 20px;
}

.search-box {
    position: relative;
    width: 100%;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 8px 12px 8px 35px;
    border: 1px solid #ccc;
    border-radius: 20px;
    font-size: 14px;
    background: white;
}

.search-box input:focus {
    outline: none;
    border-color: #0078d4;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* User Info Dropdown */
.user-info-dropdown {
    position: relative;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-size: 0.9rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.user-info:hover {
    background: rgba(255, 255, 255, 0.1);
}

.user-details {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.database-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.database-badge.prod {
    background: #dc3545;
}

.dropdown-arrow {
    font-size: 0.8rem;
    transition: transform 0.2s ease;
}

.user-info.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* User Dropdown Menu */
.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 350px;
    max-width: 400px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
}

.user-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.user-info-details strong {
    display: block;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.user-info-details small {
    opacity: 0.8;
    font-size: 0.9rem;
}

.dropdown-divider {
    height: 1px;
    background: #e0e0e0;
    margin: 0;
}

.session-info {
    padding: 1.5rem;
}

.session-info h4 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.session-details {
    display: grid;
    gap: 0.75rem;
}

.session-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f5f5f5;
}

.session-item:last-child {
    border-bottom: none;
}

.session-item .label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
}

.session-item .value {
    color: #2c3e50;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dropdown-actions {
    padding: 1rem 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.dropdown-action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 6px;
    background: #f8f9fa;
    color: #2c3e50;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.dropdown-action-btn:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.dropdown-action-btn.logout-action {
    background: #dc3545;
    color: white;
}

.dropdown-action-btn.logout-action:hover {
    background: #c82333;
}

/* Responsive Design for User Dropdown */
@media (max-width: 768px) {
    .user-dropdown-menu {
        min-width: 300px;
        max-width: 90vw;
        right: -10px;
    }

    .dropdown-header {
        padding: 1rem;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .session-info {
        padding: 1rem;
    }

    .session-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .session-item .value {
        max-width: 100%;
        width: 100%;
    }

    .dropdown-actions {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .user-dropdown-menu {
        min-width: 280px;
        right: -20px;
    }

    .user-details {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .database-badge {
        align-self: flex-start;
    }
}

.logout-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 3px;
    font-size: 16px;
    transition: background-color 0.2s;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Navigation */
.main-nav {
    background: var(--sap-secondary);
    width: 250px;
    height: calc(100vh - 60px);
    overflow-y: auto;
    box-shadow: 2px 0 4px var(--sap-shadow);
    border-right: 1px solid var(--sap-border);
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.nav-item:hover {
    background: var(--sap-primary);
    padding-left: 25px;
    transform: translateX(2px);
}

.nav-item.active {
    background: var(--sap-primary);
    border-left: 4px solid var(--sap-primary-dark);
    color: white;
}

.nav-item.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--sap-primary-dark);
}

.nav-item i {
    width: 20px;
    margin-right: 12px;
    font-size: 16px;
    color: inherit;
}

.nav-item span {
    font-size: 14px;
    font-weight: 500;
    color: inherit;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: 0;
    background: var(--sap-background);
    overflow-y: auto;
    margin-left: 250px;
    min-height: calc(100vh - 60px);
}

/* Dashboard */
.dashboard h1 {
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: 300;
}

.dashboard-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.widget {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.widget h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 500;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.quick-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border: 2px dashed #dee2e6;
    border-radius: 6px;
    background: #f8f9fa;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    font-size: 13px;
}

.quick-btn:hover {
    border-color: #0078d4;
    background: #f0f8ff;
    color: #0078d4;
    transform: translateY(-2px);
}

.quick-btn i {
    font-size: 24px;
    margin-bottom: 8px;
}

/* Module Content */
.module-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.module-title {
    color: #2c3e50;
    font-size: 24px;
    font-weight: 500;
}

.module-actions {
    display: flex;
    gap: 10px;
}

/* Toolbar */
.toolbar {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.toolbar-left {
    display: flex;
    gap: 10px;
    align-items: center;
}

.toolbar-right {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Status Bar */
.status-bar {
    background: #2c3e50;
    color: white;
    padding: 8px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    margin-top: auto;
}

.status-left {
    display: flex;
    gap: 20px;
}

.status-right {
    display: flex;
    gap: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-nav {
        width: 100%;
        height: auto;
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 1000;
        transform: translateY(100%);
        transition: transform 0.3s;
    }
    
    .main-nav.mobile-open {
        transform: translateY(0);
    }
    
    .content-area {
        margin-left: 0;
        padding-bottom: 80px;
    }
    
    .nav-menu {
        display: flex;
        overflow-x: auto;
        padding: 10px 0;
    }
    
    .nav-item {
        min-width: 100px;
        flex-direction: column;
        padding: 10px;
        text-align: center;
    }
    
    .nav-item i {
        margin-right: 0;
        margin-bottom: 5px;
    }
    
    .nav-item span {
        font-size: 12px;
    }
    
    .header-center {
        display: none;
    }
    
    .dashboard-widgets {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .app-header {
        padding: 0 15px;
    }
    
    .content-area {
        padding: 15px;
    }
    
    .company-name {
        display: none;
    }
    
    .user-info {
        flex-direction: column;
        gap: 5px;
    }
}
