/**
 * Dashboard Module
 * Maneja la funcionalidad del panel de control principal
 */
window.Dashboard = (function() {
    'use strict';

    let refreshInterval;
    let isFullscreen = false;

    /**
     * Inicializar dashboard
     */
    function init() {
        console.log('Inicializando Dashboard...');
        
        // Configurar event listeners
        setupEventListeners();
        
        // Cargar datos iniciales
        loadDashboardData();
        
        // Configurar actualización automática
        startAutoRefresh();
    }

    /**
     * Configurar event listeners
     */
    function setupEventListeners() {
        // Botón de actualizar
        const refreshBtn = document.getElementById('refresh-dashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                loadDashboardData();
                showMessage('Dashboard actualizado', 'success');
            });
        }

        // Botón de pantalla completa
        const fullscreenBtn = document.getElementById('fullscreen-dashboard');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', toggleFullscreen);
        }

        // Selector de período de métricas
        const periodSelect = document.getElementById('metrics-period');
        if (periodSelect) {
            periodSelect.addEventListener('change', (e) => {
                loadMetrics(e.target.value);
            });
        }

        // Quick actions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.quick-btn')) {
                const action = e.target.closest('.quick-btn').dataset.action;
                handleQuickAction(action);
            }
        });
    }

    /**
     * Cargar datos del dashboard
     */
    async function loadDashboardData() {
        try {
            // Actualizar estado del sistema
            await updateSystemStatus();
            
            // Cargar métricas
            const period = document.getElementById('metrics-period')?.value || 'month';
            await loadMetrics(period);
            
            // Actualizar timestamp
            updateLastUpdateTime();
            
        } catch (error) {
            console.error('Error cargando datos del dashboard:', error);
            showMessage('Error al cargar datos del dashboard', 'error');
        }
    }

    /**
     * Actualizar estado del sistema
     */
    async function updateSystemStatus() {
        try {
            // Verificar conexión SAP
            const sapStatus = document.getElementById('sap-status');
            const dbStatus = document.getElementById('db-status');
            const activeUsers = document.getElementById('active-users');

            // Simular verificación de estado (en producción, hacer llamada real a la API)
            if (sapStatus) {
                sapStatus.textContent = 'Conectado';
                sapStatus.className = 'status-value connected';
            }

            if (dbStatus) {
                dbStatus.textContent = localStorage.getItem('currentDatabase') || 'CAPA';
            }

            if (activeUsers) {
                // En producción, obtener número real de usuarios activos
                activeUsers.textContent = '1';
            }

        } catch (error) {
            console.error('Error actualizando estado del sistema:', error);
            
            const sapStatus = document.getElementById('sap-status');
            if (sapStatus) {
                sapStatus.textContent = 'Desconectado';
                sapStatus.className = 'status-value disconnected';
            }
        }
    }

    /**
     * Cargar métricas de negocio
     */
    async function loadMetrics(period = 'month') {
        try {
            // En producción, hacer llamadas reales a la API
            const metrics = await getBusinessMetrics(period);
            
            // Actualizar métricas en la UI
            updateMetricCard('sales-metric', metrics.sales);
            updateMetricCard('orders-metric', metrics.orders);
            updateMetricCard('inventory-metric', metrics.inventory);
            updateMetricCard('customers-metric', metrics.customers);
            
        } catch (error) {
            console.error('Error cargando métricas:', error);
            // Mostrar valores por defecto
            updateMetricCard('sales-metric', { value: '$0', change: '+0%' });
            updateMetricCard('orders-metric', { value: '0', change: '0 nuevas' });
            updateMetricCard('inventory-metric', { value: '0', change: '0 bajo mínimo' });
            updateMetricCard('customers-metric', { value: '0', change: '0 nuevos' });
        }
    }

    /**
     * Obtener métricas de negocio (simulado)
     */
    async function getBusinessMetrics(period) {
        // En producción, hacer llamadas reales a la API SAP B1
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    sales: { value: '$125,430', change: '+12.5%', trend: 'positive' },
                    orders: { value: '23', change: '5 nuevas', trend: 'neutral' },
                    inventory: { value: '1,247', change: '3 bajo mínimo', trend: 'warning' },
                    customers: { value: '156', change: '8 nuevos', trend: 'positive' }
                });
            }, 500);
        });
    }

    /**
     * Actualizar tarjeta de métrica
     */
    function updateMetricCard(metricId, data) {
        const metricElement = document.getElementById(metricId);
        if (metricElement) {
            metricElement.textContent = data.value;
            
            // Actualizar indicador de cambio
            const changeElement = metricElement.parentElement.querySelector('.metric-change');
            if (changeElement && data.change) {
                changeElement.textContent = data.change;
                changeElement.className = `metric-change ${data.trend || ''}`;
            }
        }
    }

    /**
     * Manejar acciones rápidas
     */
    function handleQuickAction(action) {
        console.log('Ejecutando acción rápida:', action);
        
        switch (action) {
            case 'sales-order':
                if (window.SalesModule && window.SalesModule.createSalesOrder) {
                    window.SalesModule.createSalesOrder();
                } else {
                    showMessage('Módulo de ventas no disponible', 'warning');
                }
                break;
                
            case 'purchase-order':
                if (window.PurchasingModule && window.PurchasingModule.createPurchaseOrder) {
                    window.PurchasingModule.createPurchaseOrder();
                } else {
                    showMessage('Módulo de compras no disponible', 'warning');
                }
                break;
                
            case 'item-master':
                if (window.InventoryModule && window.InventoryModule.createItem) {
                    window.InventoryModule.createItem();
                } else {
                    showMessage('Módulo de inventario no disponible', 'warning');
                }
                break;
                
            case 'business-partner':
                if (window.PartnersModule && window.PartnersModule.createBusinessPartner) {
                    window.PartnersModule.createBusinessPartner();
                } else {
                    showMessage('Módulo de socios de negocio no disponible', 'warning');
                }
                break;
                
            case 'payment':
                if (window.BankingModule && window.BankingModule.createPayment) {
                    window.BankingModule.createPayment();
                } else {
                    showMessage('Módulo de bancos no disponible', 'warning');
                }
                break;
                
            case 'journal-entry':
                if (window.FinancialsModule && window.FinancialsModule.createJournalEntry) {
                    window.FinancialsModule.createJournalEntry();
                } else {
                    showMessage('Módulo de finanzas no disponible', 'warning');
                }
                break;
                
            case 'production-order':
                if (window.ProductionModule && window.ProductionModule.createProductionOrder) {
                    window.ProductionModule.createProductionOrder();
                } else {
                    showMessage('Módulo de producción no disponible', 'warning');
                }
                break;
                
            case 'service-call':
                if (window.ServiceModule && window.ServiceModule.createServiceCall) {
                    window.ServiceModule.createServiceCall();
                } else {
                    showMessage('Módulo de servicio no disponible', 'warning');
                }
                break;
                
            default:
                showMessage(`Acción "${action}" no implementada`, 'info');
        }
    }

    /**
     * Alternar pantalla completa
     */
    function toggleFullscreen() {
        const dashboard = document.getElementById('dashboard');
        const fullscreenBtn = document.getElementById('fullscreen-dashboard');
        
        if (!isFullscreen) {
            // Entrar en pantalla completa
            dashboard.classList.add('fullscreen');
            fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i> Salir Pantalla Completa';
            isFullscreen = true;
        } else {
            // Salir de pantalla completa
            dashboard.classList.remove('fullscreen');
            fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i> Pantalla Completa';
            isFullscreen = false;
        }
    }

    /**
     * Iniciar actualización automática
     */
    function startAutoRefresh() {
        // Actualizar cada 5 minutos
        refreshInterval = setInterval(() => {
            loadDashboardData();
        }, 5 * 60 * 1000);
    }

    /**
     * Detener actualización automática
     */
    function stopAutoRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }

    /**
     * Actualizar timestamp de última actualización
     */
    function updateLastUpdateTime() {
        const lastUpdateElement = document.getElementById('last-update');
        if (lastUpdateElement) {
            const now = new Date();
            lastUpdateElement.textContent = now.toLocaleTimeString();
        }
    }

    /**
     * Mostrar mensaje
     */
    function showMessage(message, type = 'info') {
        if (window.App && window.App.showMessage) {
            window.App.showMessage(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    // API pública
    return {
        init,
        loadDashboardData,
        toggleFullscreen,
        startAutoRefresh,
        stopAutoRefresh
    };
})();

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    Dashboard.init();
});
