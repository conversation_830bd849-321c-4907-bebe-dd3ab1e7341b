/**
 * Main Application Controller
 * Handles app initialization, routing, and global state management
 */
window.App = (function() {
    'use strict';

    // Application state
    let state = {
        user: null,
        database: null,
        server: null,
        currentModule: null,
        isAuthenticated: false,
        config: null
    };

    // DOM elements
    let elements = {};

    /**
     * Initialize the application
     */
    function init() {
        console.log('Iniciando Cliente Web SAP Business One...');

        // Cache DOM elements
        cacheElements();

        // Bind events
        bindEvents();

        // Check authentication status and restore session if valid
        const isLoggedIn = checkAuthStatus();

        if (isLoggedIn) {
            // User is already authenticated, show main app
            showMainApp();
            updateUserInfo();
            loadDashboard();
            console.log('Sesión restaurada exitosamente');
        } else {
            // Show login screen
            showLogin();
        }

        console.log('Aplicación inicializada correctamente');
    }

    /**
     * Cache frequently used DOM elements
     */
    function cacheElements() {
        elements = {
            loginContainer: document.getElementById('login-container'),
            mainApp: document.getElementById('main-app'),
            loginForm: document.getElementById('login-form'),
            loginStatus: document.getElementById('login-status'),
            logoutBtn: document.getElementById('logout-btn'),
            currentUser: document.getElementById('current-user'),
            currentDatabase: document.getElementById('current-database'),
            contentArea: document.getElementById('content-area'),
            navItems: document.querySelectorAll('.nav-item'),
            loadingOverlay: document.getElementById('loading-overlay'),
            quickBtns: document.querySelectorAll('.quick-btn'),
            userInfoTrigger: document.getElementById('user-info-trigger'),
            userDropdownMenu: document.getElementById('user-dropdown-menu'),
            refreshSessionBtn: document.getElementById('refresh-session-btn')
        };
    }

    /**
     * Bind event listeners
     */
    function bindEvents() {
        // Login form submission
        if (elements.loginForm) {
            elements.loginForm.addEventListener('submit', handleLogin);
        }

        // Logout button
        if (elements.logoutBtn) {
            elements.logoutBtn.addEventListener('click', handleLogout);
        }

        // User info dropdown
        if (elements.userInfoTrigger) {
            elements.userInfoTrigger.addEventListener('click', toggleUserDropdown);
        }

        // Refresh session button
        if (elements.refreshSessionBtn) {
            elements.refreshSessionBtn.addEventListener('click', handleRefreshSession);
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (elements.userDropdownMenu &&
                !elements.userInfoTrigger.contains(e.target) &&
                !elements.userDropdownMenu.contains(e.target)) {
                closeUserDropdown();
            }
        });

        // Navigation items
        elements.navItems.forEach(item => {
            item.addEventListener('click', function() {
                const module = this.getAttribute('data-module');
                loadModule(module);
            });
        });

        // Quick action buttons
        elements.quickBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.getAttribute('data-action');
                handleQuickAction(action);
            });
        });

        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('Error de aplicación:', e.error);
            showMessage('Ha ocurrido un error inesperado', 'error');
        });

        // Handle browser back/forward
        window.addEventListener('popstate', function(e) {
            if (e.state && e.state.module) {
                loadModule(e.state.module, false);
            }
        });
    }

    /**
     * Handle login form submission
     */
    async function handleLogin(e) {
        e.preventDefault();
        
        const formData = new FormData(elements.loginForm);
        const credentials = {
            server: formData.get('server'),
            database: formData.get('database'),
            username: formData.get('username'),
            password: formData.get('password')
        };

        showLoading(true);
        clearLoginStatus();

        try {
            const response = await Auth.login(credentials);
            
            if (response.success) {
                // Update application state
                state.user = response.user;
                state.database = credentials.database;
                state.server = credentials.server;
                state.isAuthenticated = true;

                // Update UI
                updateUserInfo();
                showMainApp();
                showLoginStatus('¡Inicio de sesión exitoso!', 'success');
                
                // Load dashboard
                loadDashboard();
                
                console.log('Usuario conectado exitosamente:', response.user);
            } else {
                showLoginStatus(response.message || 'Error en el inicio de sesión', 'error');
            }
        } catch (error) {
            console.error('Error de conexión:', error);
            showLoginStatus('Error de conexión. Por favor, inténtelo de nuevo.', 'error');
        } finally {
            showLoading(false);
        }
    }

    /**
     * Handle logout
     */
    async function handleLogout() {
        if (confirm('¿Está seguro de que desea cerrar sesión?')) {
            showLoading(true);
            
            try {
                await Auth.logout();
                
                // Clear application state
                state = {
                    user: null,
                    database: null,
                    server: null,
                    currentModule: null,
                    isAuthenticated: false
                };

                // Show login screen
                showLogin();
                clearLoginStatus();
                
                console.log('Sesión cerrada exitosamente');
            } catch (error) {
                console.error('Error al cerrar sesión:', error);
            } finally {
                showLoading(false);
            }
        }
    }

    /**
     * Handle quick action buttons
     */
    function handleQuickAction(action) {
        console.log('Acción rápida:', action);
        
        switch (action) {
            case 'sales-order':
                loadModule('sales', true, { action: 'new', type: 'order' });
                break;
            case 'purchase-order':
                loadModule('purchasing', true, { action: 'new', type: 'order' });
                break;
            case 'item-master':
                loadModule('inventory', true, { action: 'new', type: 'item' });
                break;
            case 'business-partner':
                loadModule('partners', true, { action: 'new', type: 'customer' });
                break;
            default:
                console.warn('Unknown quick action:', action);
        }
    }

    /**
     * Load a module
     */
    function loadModule(moduleName, pushState = true, params = {}) {
        if (!state.isAuthenticated) {
            showLogin();
            return;
        }

        console.log('Loading module:', moduleName, params);
        
        // Update navigation active state
        updateNavigation(moduleName);
        
        // Update current module
        state.currentModule = moduleName;
        
        // Push state for browser history
        if (pushState) {
            history.pushState({ module: moduleName, params }, '', `#${moduleName}`);
        }

        // Show loading
        showLoading(true);

        // Load module content
        try {
            switch (moduleName) {
                case 'sales':
                    if (window.SalesModule) {
                        SalesModule.load(elements.contentArea, params);
                    }
                    break;
                case 'purchasing':
                    if (window.PurchasingModule) {
                        PurchasingModule.load(elements.contentArea, params);
                    }
                    break;
                case 'inventory':
                    if (window.InventoryModule) {
                        InventoryModule.load(elements.contentArea, params);
                    }
                    break;
                case 'production':
                    if (window.ProductionModule) {
                        ProductionModule.load(elements.contentArea, params);
                    }
                    break;
                case 'partners':
                    if (window.PartnersModule) {
                        PartnersModule.load(elements.contentArea, params);
                    }
                    break;
                case 'service':
                    if (window.ServiceModule) {
                        ServiceModule.load(elements.contentArea, params);
                    }
                    break;
                case 'banking':
                    if (window.BankingModule) {
                        BankingModule.load(elements.contentArea, params);
                    }
                    break;
                case 'fondos-fijos':
                    if (window.FondosFijosModule) {
                        FondosFijosModule.load(elements.contentArea, params);
                    }
                    break;
                case 'financials':
                    if (window.FinancialsModule) {
                        FinancialsModule.load(elements.contentArea, params);
                    }
                    break;
                case 'mrp':
                    if (window.MRPModule) {
                        MRPModule.load(elements.contentArea, params);
                    }
                    break;
                case 'crm':
                    if (window.CRMModule) {
                        CRMModule.load(elements.contentArea, params);
                    }
                    break;
                case 'projects':
                    if (window.ProjectsModule) {
                        ProjectsModule.load(elements.contentArea, params);
                    }
                    break;
                case 'hr':
                    if (window.HRModule) {
                        HRModule.load(elements.contentArea, params);
                    }
                    break;
                case 'assets':
                    if (window.AssetsModule) {
                        AssetsModule.load(elements.contentArea, params);
                    }
                    break;
                case 'reports':
                    if (window.ReportsModule) {
                        ReportsModule.load(elements.contentArea, params);
                    }
                    break;
                case 'administration':
                    if (window.AdministrationModule) {
                        AdministrationModule.load(elements.contentArea, params);
                    }
                    break;
                default:
                    console.warn('Unknown module:', moduleName);
                    loadDashboard();
            }
        } catch (error) {
            console.error('Error loading module:', error);
            showMessage('Error al cargar el módulo. Por favor, inténtelo de nuevo.', 'error');
            loadDashboard();
        } finally {
            showLoading(false);
        }
    }

    /**
     * Load dashboard
     */
    function loadDashboard() {
        // Update navigation
        updateNavigation(null);

        // Show the dashboard that's already in the HTML
        const dashboard = document.getElementById('dashboard');
        if (dashboard) {
            elements.contentArea.innerHTML = '';
            elements.contentArea.appendChild(dashboard.cloneNode(true));

            // Initialize dashboard functionality
            if (window.Dashboard && window.Dashboard.loadDashboardData) {
                window.Dashboard.loadDashboardData();
            }
        } else {
            // Fallback: create a simple dashboard
            elements.contentArea.innerHTML = `
                <div class="dashboard">
                    <div class="dashboard-header">
                        <h1>Panel de Control - SAP Business One</h1>
                    </div>
                    <div class="dashboard-content">
                        <p>Dashboard cargando...</p>
                        <p>Si este mensaje persiste, verifique la configuración del sistema.</p>
                    </div>
                </div>
            `;
        }

        // Update URL
        history.pushState({ module: 'dashboard' }, '', '#dashboard');
    }



    /**
     * Update navigation active state
     */
    function updateNavigation(activeModule) {
        elements.navItems.forEach(item => {
            const module = item.getAttribute('data-module');
            if (module === activeModule) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    /**
     * Show login screen
     */
    function showLogin() {
        elements.loginContainer.classList.add('active');
        elements.mainApp.classList.remove('active');
        document.title = 'SAP Business One - Login';
    }

    /**
     * Show main application
     */
    function showMainApp() {
        elements.loginContainer.classList.remove('active');
        elements.mainApp.classList.add('active');
        document.title = 'SAP Business One - Web Client';
    }

    /**
     * Update user information in header
     */
    function updateUserInfo() {
        if (state.user) {
            elements.currentUser.textContent = state.user.username;
        }

        if (state.database) {
            elements.currentDatabase.textContent = state.database;
            elements.currentDatabase.className = `database-badge ${state.database === 'SBO_ECOM' ? 'prod' : ''}`;
        }

        // Update dropdown user info
        updateUserDropdownInfo();
    }

    /**
     * Update user dropdown with session information
     */
    function updateUserDropdownInfo() {
        if (!state.user) return;

        // Update dropdown header
        const dropdownUsername = document.getElementById('dropdown-username');
        const dropdownEmail = document.getElementById('dropdown-email');

        if (dropdownUsername) {
            dropdownUsername.textContent = state.user.fullName || state.user.username || 'Usuario';
        }

        if (dropdownEmail) {
            dropdownEmail.textContent = state.user.email || `${state.user.username}@empresa.com`;
        }

        // Update session details
        updateSessionDetails();
    }

    /**
     * Update session details in dropdown
     */
    function updateSessionDetails() {
        if (!state.user) return;

        const sapInfo = state.user.sapSessionInfo || {};

        const sessionDetails = {
            'session-username': state.user.username || '-',
            'session-database': state.database || '-',
            'session-server': state.server || '-',
            'session-id': state.user.sessionId || '-',
            'session-login-time': state.user.loginTime ? formatDateTime(state.user.loginTime) : '-',
            'session-last-activity': formatDateTime(new Date().toISOString()),
            'session-permissions': state.user.permissions ? state.user.permissions.join(', ') : 'Usuario estándar'
        };

        // Update each session detail element
        Object.keys(sessionDetails).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = sessionDetails[id];
                element.title = sessionDetails[id]; // Add tooltip for long values
            }
        });

        // Add additional SAP session info if available
        if (sapInfo && Object.keys(sapInfo).length > 0) {
            addAdditionalSAPInfo(sapInfo);
        }
    }

    /**
     * Add additional SAP session information to dropdown
     */
    function addAdditionalSAPInfo(sapInfo) {
        const sessionDetailsContainer = document.getElementById('session-details');
        if (!sessionDetailsContainer) return;

        // Check if additional info already exists
        let additionalInfoContainer = document.getElementById('additional-sap-info');
        if (!additionalInfoContainer) {
            additionalInfoContainer = document.createElement('div');
            additionalInfoContainer.id = 'additional-sap-info';
            additionalInfoContainer.innerHTML = `
                <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #e0e0e0;">
                    <h5 style="margin: 0 0 0.75rem 0; color: #2c3e50; font-size: 0.9rem;">
                        <i class="fas fa-cogs"></i> Información SAP Adicional
                    </h5>
                    <div id="sap-info-details"></div>
                </div>
            `;
            sessionDetailsContainer.appendChild(additionalInfoContainer);
        }

        const sapInfoDetails = document.getElementById('sap-info-details');
        if (sapInfoDetails) {
            const sapInfoItems = [
                { label: 'Código Usuario', value: sapInfo.userCode || '-' },
                { label: 'Firma Usuario', value: sapInfo.userSign || '-' },
                { label: 'Timeout Sesión', value: sapInfo.sessionTimeout ? `${sapInfo.sessionTimeout} min` : '-' },
                { label: 'Versión SAP', value: sapInfo.version || '-' },
                { label: 'Idioma', value: sapInfo.language || '-' },
                { label: 'Localización', value: sapInfo.localization || '-' },
                { label: 'Moneda', value: sapInfo.currency || '-' },
                { label: 'Formato Fecha', value: sapInfo.dateFormat || '-' },
                { label: 'Separador Decimal', value: sapInfo.decimalSeparator || '-' },
                { label: 'Separador Miles', value: sapInfo.thousandsSeparator || '-' },
                { label: 'Hora Servidor', value: sapInfo.serverTime ? formatDateTime(sapInfo.serverTime) : '-' },
                { label: 'IP Cliente', value: sapInfo.clientIP || '-' },
                { label: 'IP Servidor', value: sapInfo.serverIP || '-' }
            ];

            sapInfoDetails.innerHTML = sapInfoItems.map(item => `
                <div class="session-item">
                    <span class="label">${item.label}:</span>
                    <span class="value" title="${item.value}">${item.value}</span>
                </div>
            `).join('');
        }
    }

    /**
     * Show/hide loading overlay
     */
    function showLoading(show) {
        if (show) {
            elements.loadingOverlay.classList.add('active');
        } else {
            elements.loadingOverlay.classList.remove('active');
        }
    }

    /**
     * Show login status message
     */
    function showLoginStatus(message, type) {
        elements.loginStatus.textContent = message;
        elements.loginStatus.className = `login-status ${type}`;
    }

    /**
     * Clear login status
     */
    function clearLoginStatus() {
        elements.loginStatus.textContent = '';
        elements.loginStatus.className = 'login-status';
    }

    /**
     * Show general message (could be expanded to show toasts)
     */
    function showMessage(message, type) {
        console.log(`[${type.toUpperCase()}] ${message}`);
        // Could implement toast notifications here
        alert(message);
    }

    /**
     * Check authentication status on app start
     */
    function checkAuthStatus() {
        try {
            // Check if user session exists
            const savedUser = localStorage.getItem('sap_user');
            const savedSession = localStorage.getItem('sap_session');
            const savedDatabase = localStorage.getItem('sap_database');
            const savedServer = localStorage.getItem('sap_server');

            if (savedUser && savedSession && savedDatabase && savedServer) {
                // Parse and restore user data
                const userData = JSON.parse(savedUser);

                // Check if session is not expired (24 hours)
                const loginTime = new Date(userData.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

                if (hoursDiff < 24) {
                    // Session is still valid, restore state
                    state.user = userData;
                    state.database = savedDatabase;
                    state.server = savedServer;
                    state.isAuthenticated = true;

                    console.log('Sesión válida encontrada, restaurando estado...');
                    return true;
                } else {
                    // Session expired, clear storage
                    console.log('Sesión expirada, limpiando datos...');
                    clearSessionData();
                }
            }
        } catch (error) {
            console.error('Error al verificar estado de autenticación:', error);
            clearSessionData();
        }

        return false;
    }

    /**
     * Clear all session data
     */
    function clearSessionData() {
        localStorage.removeItem('sap_user');
        localStorage.removeItem('sap_session');
        localStorage.removeItem('sap_database');
        localStorage.removeItem('sap_server');

        state.user = null;
        state.database = null;
        state.server = null;
        state.isAuthenticated = false;
    }



    /**
     * Toggle user dropdown menu
     */
    function toggleUserDropdown() {
        if (!elements.userDropdownMenu || !elements.userInfoTrigger) return;

        const isOpen = elements.userDropdownMenu.classList.contains('show');

        if (isOpen) {
            closeUserDropdown();
        } else {
            openUserDropdown();
        }
    }

    /**
     * Open user dropdown menu
     */
    function openUserDropdown() {
        if (!elements.userDropdownMenu || !elements.userInfoTrigger) return;

        elements.userInfoTrigger.classList.add('active');
        elements.userDropdownMenu.classList.add('show');

        // Update session info when opening
        updateSessionDetails();
    }

    /**
     * Close user dropdown menu
     */
    function closeUserDropdown() {
        if (!elements.userDropdownMenu || !elements.userInfoTrigger) return;

        elements.userInfoTrigger.classList.remove('active');
        elements.userDropdownMenu.classList.remove('show');
    }

    /**
     * Handle refresh session
     */
    async function handleRefreshSession() {
        try {
            showLoading(true);

            const response = await Auth.refreshToken();

            if (response.success) {
                // Update session info
                if (response.sessionId) {
                    state.user.sessionId = response.sessionId;
                    state.user.lastRefresh = new Date().toISOString();
                }

                updateSessionDetails();
                showMessage('Sesión actualizada exitosamente', 'success');
            } else {
                showMessage('Error al actualizar la sesión: ' + response.message, 'error');
            }
        } catch (error) {
            console.error('Error refreshing session:', error);
            showMessage('Error al actualizar la sesión', 'error');
        } finally {
            showLoading(false);
        }
    }

    /**
     * Format date and time for display
     */
    function formatDateTime(dateString) {
        try {
            const date = new Date(dateString);
            return date.toLocaleString('es-ES', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            return dateString;
        }
    }

    /**
     * Get current application state
     */
    function getState() {
        return { ...state };
    }

    /**
     * Public API
     */
    return {
        init,
        loadModule,
        loadDashboard,
        showLoading,
        showMessage,
        getState
    };
})();
