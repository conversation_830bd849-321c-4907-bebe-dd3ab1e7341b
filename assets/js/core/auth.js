/**
 * Authentication Module
 * Handles user authentication, session management
 */
window.Auth = (function() {
    'use strict';

    /**
     * Login user with credentials
     */
    async function login(credentials) {
        try {
            console.log('Intentando iniciar sesión para:', credentials.username, 'en', credentials.database);
            
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // For demo purposes, accept any credentials
            // In real implementation, this would call SAP B1 Service Layer API
            if (credentials.username && credentials.password) {
                const user = {
                    username: credentials.username,
                    fullName: `${credentials.username} Usuario`,
                    email: `${credentials.username}@empresa.com`,
                    database: credentials.database,
                    server: credentials.server,
                    sessionId: generateSessionId(),
                    loginTime: new Date().toISOString(),
                    lastActivity: new Date().toISOString(),
                    permissions: getDefaultPermissions(),
                    // Additional SAP B1 session information
                    sapSessionInfo: {
                        companyDB: credentials.database,
                        userCode: credentials.username,
                        userSign: Math.floor(Math.random() * 1000),
                        sessionTimeout: 30, // minutes
                        licenseServer: credentials.server,
                        version: '10.0.0.0',
                        language: 'es-ES',
                        localization: 'Spain',
                        currency: 'EUR',
                        dateFormat: 'dd/MM/yyyy',
                        decimalSeparator: ',',
                        thousandsSeparator: '.',
                        serverTime: new Date().toISOString(),
                        clientIP: '************0',
                        serverIP: credentials.server || '************'
                    }
                };

                // Save user session
                localStorage.setItem('sap_user', JSON.stringify(user));
                localStorage.setItem('sap_session', user.sessionId);
                localStorage.setItem('sap_database', credentials.database);
                localStorage.setItem('sap_server', credentials.server);

                return {
                    success: true,
                    user: user,
                    message: 'Inicio de sesión exitoso'
                };
            } else {
                return {
                    success: false,
                    message: 'Usuario o contraseña inválidos'
                };
            }
        } catch (error) {
            console.error('Error de inicio de sesión:', error);
            return {
                success: false,
                message: 'Error de conexión. Por favor, verifique la configuración del servidor.'
            };
        }
    }

    /**
     * Logout user
     */
    async function logout() {
        try {
            console.log('Cerrando sesión de usuario...');
            
            // Clear session data
            localStorage.removeItem('sap_user');
            localStorage.removeItem('sap_session');
            localStorage.removeItem('sap_database');
            localStorage.removeItem('sap_server');
            
            // In real implementation, would call API to invalidate session
            await new Promise(resolve => setTimeout(resolve, 500));
            
            return {
                success: true,
                message: 'Cierre de sesión exitoso'
            };
        } catch (error) {
            console.error('Error al cerrar sesión:', error);
            return {
                success: false,
                message: 'Error durante el cierre de sesión'
            };
        }
    }

    /**
     * Check if user is authenticated
     */
    function isAuthenticated() {
        const session = localStorage.getItem('sap_session');
        const user = localStorage.getItem('sap_user');
        return !!(session && user);
    }

    /**
     * Get current user
     */
    function getCurrentUser() {
        try {
            const userStr = localStorage.getItem('sap_user');
            if (userStr) {
                return JSON.parse(userStr);
            }
        } catch (error) {
            console.error('Error al analizar datos de usuario:', error);
        }
        return null;
    }

    /**
     * Get current session
     */
    function getCurrentSession() {
        return localStorage.getItem('sap_session');
    }

    /**
     * Get current database
     */
    function getCurrentDatabase() {
        return localStorage.getItem('sap_database');
    }

    /**
     * Get current server
     */
    function getCurrentServer() {
        return localStorage.getItem('sap_server');
    }

    /**
     * Validate session (could be called periodically)
     */
    async function validateSession() {
        try {
            const session = getCurrentSession();
            if (!session) {
                return false;
            }

            // In real implementation, would validate with server
            // For now, just check if session exists
            return true;
        } catch (error) {
            console.error('Error de validación de sesión:', error);
            return false;
        }
    }

    /**
     * Refresh authentication token
     */
    async function refreshToken() {
        try {
            const user = getCurrentUser();
            if (!user) {
                throw new Error('Usuario no encontrado');
            }

            // In real implementation, would call refresh token API
            const newSessionId = generateSessionId();
            user.sessionId = newSessionId;
            user.lastRefresh = new Date().toISOString();

            localStorage.setItem('sap_user', JSON.stringify(user));
            localStorage.setItem('sap_session', newSessionId);

            return {
                success: true,
                sessionId: newSessionId
            };
        } catch (error) {
            console.error('Error al actualizar token:', error);
            return {
                success: false,
                message: 'Error al actualizar el token'
            };
        }
    }

    /**
     * Change password
     */
    async function changePassword(oldPassword, newPassword) {
        try {
            // In real implementation, would call SAP B1 API
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Simulate validation
            if (oldPassword && newPassword && newPassword.length >= 6) {
                return {
                    success: true,
                    message: 'Contraseña cambiada exitosamente'
                };
            } else {
                return {
                    success: false,
                    message: 'Contraseña inválida. La contraseña debe tener al menos 6 caracteres.'
                };
            }
        } catch (error) {
            console.error('Error al cambiar contraseña:', error);
            return {
                success: false,
                message: 'Error al cambiar la contraseña'
            };
        }
    }

    /**
     * Get user permissions
     */
    function getUserPermissions() {
        const user = getCurrentUser();
        return user ? user.permissions : [];
    }

    /**
     * Check if user has specific permission
     */
    function hasPermission(permission) {
        const permissions = getUserPermissions();
        return permissions.includes(permission) || permissions.includes('ADMIN');
    }

    /**
     * Generate session ID
     */
    function generateSessionId() {
        return 'sess_' + Math.random().toString(36).substring(2, 11) + '_' + Date.now();
    }

    /**
     * Get default permissions for demo
     */
    function getDefaultPermissions() {
        return [
            'SALES_VIEW',
            'SALES_CREATE',
            'SALES_EDIT',
            'PURCHASING_VIEW',
            'PURCHASING_CREATE',
            'PURCHASING_EDIT',
            'INVENTORY_VIEW',
            'INVENTORY_CREATE',
            'INVENTORY_EDIT',
            'PARTNERS_VIEW',
            'PARTNERS_CREATE',
            'PARTNERS_EDIT',
            'BANKING_VIEW',
            'FINANCIALS_VIEW',
            'REPORTS_VIEW'
        ];
    }

    /**
     * Setup automatic session refresh
     */
    function setupSessionRefresh() {
        // Refresh token every 25 minutes (if session timeout is 30 minutes)
        setInterval(async () => {
            if (isAuthenticated()) {
                console.log('Actualizando token de sesión...');
                await refreshToken();
            }
        }, 25 * 60 * 1000);
    }

    /**
     * Initialize authentication module
     */
    function init() {
        console.log('Inicializando módulo de autenticación...');
        setupSessionRefresh();
    }

    /**
     * Public API
     */
    return {
        init,
        login,
        logout,
        isAuthenticated,
        getCurrentUser,
        getCurrentSession,
        getCurrentDatabase,
        getCurrentServer,
        validateSession,
        refreshToken,
        changePassword,
        getUserPermissions,
        hasPermission
    };
})();

// Initialize auth module when loaded
document.addEventListener('DOMContentLoaded', function() {
    Auth.init();
});
