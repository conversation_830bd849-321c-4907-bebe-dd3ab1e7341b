/**
 * Módulo de Recursos Humanos
 * Maneja funcionalidad relacionada con empleados y nómina
 */
window.HRModule = (function() {
    'use strict';

    /**
     * Inicializar módulo de recursos humanos
     */
    function init() {
        console.log('Inicializando módulo de Recursos Humanos...');
    }

    /**
     * Cargar contenido del módulo
     */
    function load(container, params = {}) {
        if (!container) return;

        const view = params.view || 'dashboard';
        
        switch (view) {
            case 'dashboard':
                loadDashboard(container);
                break;
            case 'employees':
                loadEmployees(container);
                break;
            case 'payroll':
                loadPayroll(container);
                break;
            case 'attendance':
                loadAttendance(container);
                break;
            default:
                loadDashboard(container);
        }
    }

    /**
     * Cargar dashboard de recursos humanos
     */
    function loadDashboard(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-user-tie"></i> Recursos Humanos</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="HRModule.createEmployee()">
                            <i class="fas fa-plus"></i>
                            Nuevo Empleado
                        </button>
                        <button class="btn btn-secondary" onclick="HRModule.load(document.getElementById('content-area'), {view: 'payroll'})">
                            <i class="fas fa-money-bill"></i>
                            Nómina
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Total Empleados</h3>
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">47</div>
                                <div class="metric-label">Activos</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="HRModule.load(document.getElementById('content-area'), {view: 'employees'})">
                                        Ver Todos
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Presentes Hoy</h3>
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">42</div>
                                <div class="metric-label">89% Asistencia</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="HRModule.load(document.getElementById('content-area'), {view: 'attendance'})">
                                        Ver Detalle
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Nómina Mensual</h3>
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">$125K</div>
                                <div class="metric-label">Este Mes</div>
                                <div class="metric-change positive">****%</div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Vacaciones Pendientes</h3>
                                <i class="fas fa-calendar-times"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">23</div>
                                <div class="metric-label">Solicitudes</div>
                                <div class="metric-change warning">Requiere aprobación</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="hr-alerts">
                        <h3>Alertas de RH</h3>
                        <div class="alerts-list">
                            <div class="alert-item warning">
                                <i class="fas fa-birthday-cake"></i>
                                <div class="alert-content">
                                    <span class="alert-title">Cumpleaños Hoy</span>
                                    <span class="alert-description">María García cumple años hoy</span>
                                </div>
                                <span class="alert-time">Hoy</span>
                            </div>
                            <div class="alert-item info">
                                <i class="fas fa-calendar-check"></i>
                                <div class="alert-content">
                                    <span class="alert-title">Evaluación Pendiente</span>
                                    <span class="alert-description">Juan Pérez - Evaluación anual vence en 5 días</span>
                                </div>
                                <span class="alert-time">Vence pronto</span>
                            </div>
                            <div class="alert-item critical">
                                <i class="fas fa-file-contract"></i>
                                <div class="alert-content">
                                    <span class="alert-title">Contrato por Vencer</span>
                                    <span class="alert-description">Carlos López - Contrato vence el 28/02/2024</span>
                                </div>
                                <span class="alert-time">15 días</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recent-activity">
                        <h3>Actividad Reciente</h3>
                        <div class="activity-list">
                            <div class="activity-item">
                                <i class="fas fa-user-plus text-success"></i>
                                <span>Nuevo empleado Ana Martínez registrado</span>
                                <span class="activity-time">Hace 2 horas</span>
                            </div>
                            <div class="activity-item">
                                <i class="fas fa-calendar text-primary"></i>
                                <span>Solicitud de vacaciones aprobada - Luis Rodríguez</span>
                                <span class="activity-time">Hace 4 horas</span>
                            </div>
                            <div class="activity-item">
                                <i class="fas fa-money-bill text-info"></i>
                                <span>Nómina de enero procesada</span>
                                <span class="activity-time">Ayer</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Cargar empleados
     */
    function loadEmployees(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-users"></i> Empleados</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="HRModule.createEmployee()">
                            <i class="fas fa-plus"></i>
                            Nuevo Empleado
                        </button>
                        <button class="btn btn-secondary" onclick="HRModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="data-table-container">
                        <div class="table-controls">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Buscar empleados..." id="employees-search">
                            </div>
                            <div class="table-actions">
                                <select id="employees-department-filter">
                                    <option value="">Todos los Departamentos</option>
                                    <option value="it">Tecnología</option>
                                    <option value="sales">Ventas</option>
                                    <option value="finance">Finanzas</option>
                                    <option value="hr">Recursos Humanos</option>
                                </select>
                                <select id="employees-status-filter">
                                    <option value="">Todos los Estados</option>
                                    <option value="active">Activo</option>
                                    <option value="inactive">Inactivo</option>
                                    <option value="vacation">En Vacaciones</option>
                                </select>
                                <button class="btn btn-secondary" onclick="HRModule.exportEmployees()">
                                    <i class="fas fa-download"></i>
                                    Exportar
                                </button>
                            </div>
                        </div>
                        
                        <div class="data-table-wrapper">
                            <table class="data-table" id="employees-table">
                                <thead>
                                    <tr>
                                        <th data-sort="employeeId">ID</th>
                                        <th data-sort="name">Nombre</th>
                                        <th data-sort="position">Cargo</th>
                                        <th data-sort="department">Departamento</th>
                                        <th data-sort="salary">Salario</th>
                                        <th data-sort="hireDate">Fecha Ingreso</th>
                                        <th data-sort="status">Estado</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody id="employees-tbody">
                                    <tr>
                                        <td colspan="8" class="text-center">Cargando empleados...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="table-pagination">
                            <div class="pagination-info">
                                Mostrando <span id="showing-start">0</span> - <span id="showing-end">0</span> de <span id="total-records">0</span> registros
                            </div>
                            <div class="pagination-controls">
                                <select id="page-size">
                                    <option value="25">25</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="250">250</option>
                                    <option value="500">500</option>
                                </select>
                                <button class="btn btn-sm" id="prev-page" disabled>Anterior</button>
                                <span id="page-info">Página 1 de 1</span>
                                <button class="btn btn-sm" id="next-page" disabled>Siguiente</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Cargar datos de empleados
        loadEmployeesData();
    }

    /**
     * Cargar nómina
     */
    function loadPayroll(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-money-bill"></i> Nómina</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="HRModule.processPayroll()">
                            <i class="fas fa-play"></i>
                            Procesar Nómina
                        </button>
                        <button class="btn btn-secondary" onclick="HRModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Nómina se cargará aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar asistencia
     */
    function loadAttendance(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-user-check"></i> Control de Asistencia</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="HRModule.markAttendance()">
                            <i class="fas fa-check"></i>
                            Marcar Asistencia
                        </button>
                        <button class="btn btn-secondary" onclick="HRModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Control de asistencia se cargará aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar datos de empleados (simulado)
     */
    async function loadEmployeesData() {
        const mockData = [
            {
                employeeId: 'EMP001',
                name: 'Juan Pérez',
                position: 'Desarrollador Senior',
                department: 'Tecnología',
                salary: 5500,
                hireDate: '2022-03-15',
                status: 'active'
            },
            {
                employeeId: 'EMP002',
                name: 'María García',
                position: 'Gerente de Ventas',
                department: 'Ventas',
                salary: 6200,
                hireDate: '2021-08-10',
                status: 'active'
            }
        ];

        const tbody = document.getElementById('employees-tbody');
        if (tbody) {
            tbody.innerHTML = mockData.map(emp => `
                <tr ondblclick="HRModule.viewEmployee('${emp.employeeId}')">
                    <td>${emp.employeeId}</td>
                    <td>${emp.name}</td>
                    <td>${emp.position}</td>
                    <td>${emp.department}</td>
                    <td>$${emp.salary.toLocaleString()}</td>
                    <td>${emp.hireDate}</td>
                    <td><span class="status-badge ${emp.status}">${getStatusLabel(emp.status)}</span></td>
                    <td>
                        <button class="btn btn-sm" onclick="HRModule.viewEmployee('${emp.employeeId}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm" onclick="HRModule.editEmployee('${emp.employeeId}')">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }
    }

    /**
     * Obtener etiqueta de estado
     */
    function getStatusLabel(status) {
        const labels = {
            'active': 'Activo',
            'inactive': 'Inactivo',
            'vacation': 'En Vacaciones'
        };
        return labels[status] || status;
    }

    /**
     * Crear nuevo empleado
     */
    function createEmployee() {
        console.log('Creando nuevo empleado...');
        showMessage('Función de nuevo empleado no implementada aún', 'info');
    }

    /**
     * Ver empleado
     */
    function viewEmployee(employeeId) {
        console.log('Viendo empleado:', employeeId);
        showMessage(`Viendo empleado ${employeeId}`, 'info');
    }

    /**
     * Editar empleado
     */
    function editEmployee(employeeId) {
        console.log('Editando empleado:', employeeId);
        showMessage(`Editando empleado ${employeeId}`, 'info');
    }

    /**
     * Procesar nómina
     */
    function processPayroll() {
        console.log('Procesando nómina...');
        showMessage('Función de procesamiento de nómina no implementada aún', 'info');
    }

    /**
     * Marcar asistencia
     */
    function markAttendance() {
        console.log('Marcando asistencia...');
        showMessage('Función de marcado de asistencia no implementada aún', 'info');
    }

    /**
     * Exportar empleados
     */
    function exportEmployees() {
        console.log('Exportando empleados...');
        showMessage('Función de exportación no implementada aún', 'info');
    }

    /**
     * Mostrar mensaje
     */
    function showMessage(message, type = 'info') {
        if (window.App && window.App.showMessage) {
            window.App.showMessage(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    // API pública
    return {
        init,
        load,
        createEmployee,
        viewEmployee,
        editEmployee,
        processPayroll,
        markAttendance,
        exportEmployees
    };
})();

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    HRModule.init();
});
