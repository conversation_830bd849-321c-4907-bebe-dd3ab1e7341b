/**
 * Módulo de Producción
 * Maneja funcionalidad relacionada con manufactura y producción
 */
window.ProductionModule = (function() {
    'use strict';

    let currentView = 'list';
    let currentData = [];

    /**
     * Inicializar módulo de producción
     */
    function init() {
        console.log('Inicializando módulo de Producción...');
    }

    /**
     * Cargar contenido del módulo
     */
    function load(container, params = {}) {
        if (!container) return;

        const view = params.view || 'dashboard';
        
        switch (view) {
            case 'dashboard':
                loadDashboard(container);
                break;
            case 'production-orders':
                loadProductionOrders(container);
                break;
            case 'bills-of-materials':
                loadBillsOfMaterials(container);
                break;
            case 'resources':
                loadResources(container);
                break;
            default:
                loadDashboard(container);
        }
    }

    /**
     * Cargar dashboard de producción
     */
    function loadDashboard(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-industry"></i> Producción</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="ProductionModule.createProductionOrder()">
                            <i class="fas fa-plus"></i>
                            Nueva Orden de Producción
                        </button>
                        <button class="btn btn-secondary" onclick="ProductionModule.load(document.getElementById('content-area'), {view: 'bills-of-materials'})">
                            <i class="fas fa-list"></i>
                            Lista de Materiales
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Órdenes de Producción</h3>
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">15</div>
                                <div class="metric-label">Activas</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="ProductionModule.load(document.getElementById('content-area'), {view: 'production-orders'})">
                                        Ver Todas
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Listas de Materiales</h3>
                                <i class="fas fa-list-ul"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">42</div>
                                <div class="metric-label">Registradas</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="ProductionModule.load(document.getElementById('content-area'), {view: 'bills-of-materials'})">
                                        Ver Todas
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Recursos</h3>
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">8</div>
                                <div class="metric-label">Disponibles</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="ProductionModule.load(document.getElementById('content-area'), {view: 'resources'})">
                                        Ver Todos
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Eficiencia</h3>
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">87%</div>
                                <div class="metric-label">Este Mes</div>
                                <div class="metric-change positive">+5%</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recent-activity">
                        <h3>Actividad Reciente</h3>
                        <div class="activity-list">
                            <div class="activity-item">
                                <i class="fas fa-play text-success"></i>
                                <span>Orden de Producción OP-001 iniciada</span>
                                <span class="activity-time">Hace 2 horas</span>
                            </div>
                            <div class="activity-item">
                                <i class="fas fa-check text-primary"></i>
                                <span>Lista de Materiales BOM-045 completada</span>
                                <span class="activity-time">Hace 4 horas</span>
                            </div>
                            <div class="activity-item">
                                <i class="fas fa-pause text-warning"></i>
                                <span>Recurso R-003 en mantenimiento</span>
                                <span class="activity-time">Hace 6 horas</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Cargar órdenes de producción
     */
    function loadProductionOrders(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-cogs"></i> Órdenes de Producción</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="ProductionModule.createProductionOrder()">
                            <i class="fas fa-plus"></i>
                            Nueva Orden
                        </button>
                        <button class="btn btn-secondary" onclick="ProductionModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="data-table-container">
                        <div class="table-controls">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Buscar órdenes..." id="production-search">
                            </div>
                            <div class="table-actions">
                                <select id="production-status-filter">
                                    <option value="">Todos los Estados</option>
                                    <option value="planned">Planificada</option>
                                    <option value="released">Liberada</option>
                                    <option value="started">Iniciada</option>
                                    <option value="completed">Completada</option>
                                    <option value="cancelled">Cancelada</option>
                                </select>
                                <button class="btn btn-secondary" onclick="ProductionModule.exportData()">
                                    <i class="fas fa-download"></i>
                                    Exportar
                                </button>
                            </div>
                        </div>
                        
                        <div class="data-table-wrapper">
                            <table class="data-table" id="production-orders-table">
                                <thead>
                                    <tr>
                                        <th data-sort="docNum">Número</th>
                                        <th data-sort="itemCode">Artículo</th>
                                        <th data-sort="plannedQty">Cantidad Planificada</th>
                                        <th data-sort="completedQty">Cantidad Completada</th>
                                        <th data-sort="status">Estado</th>
                                        <th data-sort="startDate">Fecha Inicio</th>
                                        <th data-sort="dueDate">Fecha Vencimiento</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody id="production-orders-tbody">
                                    <tr>
                                        <td colspan="8" class="text-center">Cargando órdenes de producción...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="table-pagination">
                            <div class="pagination-info">
                                Mostrando <span id="showing-start">0</span> - <span id="showing-end">0</span> de <span id="total-records">0</span> registros
                            </div>
                            <div class="pagination-controls">
                                <select id="page-size">
                                    <option value="25">25</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="250">250</option>
                                    <option value="500">500</option>
                                </select>
                                <button class="btn btn-sm" id="prev-page" disabled>Anterior</button>
                                <span id="page-info">Página 1 de 1</span>
                                <button class="btn btn-sm" id="next-page" disabled>Siguiente</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Cargar datos de órdenes de producción
        loadProductionOrdersData();
    }

    /**
     * Crear nueva orden de producción
     */
    function createProductionOrder() {
        console.log('Creando nueva orden de producción...');
        showMessage('Función de nueva orden de producción no implementada aún', 'info');
    }

    /**
     * Cargar listas de materiales
     */
    function loadBillsOfMaterials(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-list-ul"></i> Listas de Materiales (BOM)</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="ProductionModule.createBOM()">
                            <i class="fas fa-plus"></i>
                            Nueva Lista
                        </button>
                        <button class="btn btn-secondary" onclick="ProductionModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Lista de materiales se cargará aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar recursos
     */
    function loadResources(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-tools"></i> Recursos de Producción</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="ProductionModule.createResource()">
                            <i class="fas fa-plus"></i>
                            Nuevo Recurso
                        </button>
                        <button class="btn btn-secondary" onclick="ProductionModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Recursos de producción se cargarán aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar datos de órdenes de producción (simulado)
     */
    async function loadProductionOrdersData() {
        // Simular datos de órdenes de producción
        const mockData = [
            {
                docNum: 'OP-001',
                itemCode: 'PROD-001',
                itemName: 'Producto Terminado A',
                plannedQty: 100,
                completedQty: 75,
                status: 'started',
                startDate: '2024-01-15',
                dueDate: '2024-01-25'
            },
            {
                docNum: 'OP-002',
                itemCode: 'PROD-002',
                itemName: 'Producto Terminado B',
                plannedQty: 50,
                completedQty: 50,
                status: 'completed',
                startDate: '2024-01-10',
                dueDate: '2024-01-20'
            }
        ];

        const tbody = document.getElementById('production-orders-tbody');
        if (tbody) {
            tbody.innerHTML = mockData.map(order => `
                <tr ondblclick="ProductionModule.viewProductionOrder('${order.docNum}')">
                    <td>${order.docNum}</td>
                    <td>${order.itemCode}</td>
                    <td>${order.plannedQty}</td>
                    <td>${order.completedQty}</td>
                    <td><span class="status-badge ${order.status}">${getStatusLabel(order.status)}</span></td>
                    <td>${order.startDate}</td>
                    <td>${order.dueDate}</td>
                    <td>
                        <button class="btn btn-sm" onclick="ProductionModule.viewProductionOrder('${order.docNum}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm" onclick="ProductionModule.editProductionOrder('${order.docNum}')">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }
    }

    /**
     * Obtener etiqueta de estado
     */
    function getStatusLabel(status) {
        const labels = {
            'planned': 'Planificada',
            'released': 'Liberada',
            'started': 'Iniciada',
            'completed': 'Completada',
            'cancelled': 'Cancelada'
        };
        return labels[status] || status;
    }

    /**
     * Ver orden de producción
     */
    function viewProductionOrder(docNum) {
        console.log('Viendo orden de producción:', docNum);
        showMessage(`Viendo orden de producción ${docNum}`, 'info');
    }

    /**
     * Editar orden de producción
     */
    function editProductionOrder(docNum) {
        console.log('Editando orden de producción:', docNum);
        showMessage(`Editando orden de producción ${docNum}`, 'info');
    }

    /**
     * Crear nueva lista de materiales
     */
    function createBOM() {
        console.log('Creando nueva lista de materiales...');
        showMessage('Función de nueva lista de materiales no implementada aún', 'info');
    }

    /**
     * Crear nuevo recurso
     */
    function createResource() {
        console.log('Creando nuevo recurso...');
        showMessage('Función de nuevo recurso no implementada aún', 'info');
    }

    /**
     * Exportar datos
     */
    function exportData() {
        console.log('Exportando datos de producción...');
        showMessage('Función de exportación no implementada aún', 'info');
    }

    /**
     * Mostrar mensaje
     */
    function showMessage(message, type = 'info') {
        if (window.App && window.App.showMessage) {
            window.App.showMessage(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    // API pública
    return {
        init,
        load,
        createProductionOrder,
        viewProductionOrder,
        editProductionOrder,
        createBOM,
        createResource,
        exportData
    };
})();

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    ProductionModule.init();
});
