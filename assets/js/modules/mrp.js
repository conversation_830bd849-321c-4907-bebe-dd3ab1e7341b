/**
 * <PERSON><PERSON><PERSON><PERSON> de MRP (Material Requirements Planning)
 * Maneja funcionalidad relacionada con planificación de materiales
 */
window.MRPModule = (function() {
    'use strict';

    let currentView = 'list';
    let currentData = [];

    /**
     * Inicializar módulo de MRP
     */
    function init() {
        console.log('Inicializando módulo de MRP...');
    }

    /**
     * Cargar contenido del módulo
     */
    function load(container, params = {}) {
        if (!container) return;

        const view = params.view || 'dashboard';
        
        switch (view) {
            case 'dashboard':
                loadDashboard(container);
                break;
            case 'planning':
                loadPlanning(container);
                break;
            case 'forecasting':
                loadForecasting(container);
                break;
            case 'recommendations':
                loadRecommendations(container);
                break;
            default:
                loadDashboard(container);
        }
    }

    /**
     * Cargar dashboard de MRP
     */
    function loadDashboard(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-project-diagram"></i> MRP - Planificación de Materiales</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="MRPModule.runMRP()">
                            <i class="fas fa-play"></i>
                            Ejecutar MRP
                        </button>
                        <button class="btn btn-secondary" onclick="MRPModule.load(document.getElementById('content-area'), {view: 'planning'})">
                            <i class="fas fa-calendar-alt"></i>
                            Planificación
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Órdenes Sugeridas</h3>
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">18</div>
                                <div class="metric-label">Pendientes</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="MRPModule.load(document.getElementById('content-area'), {view: 'recommendations'})">
                                        Ver Todas
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Items Críticos</h3>
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">5</div>
                                <div class="metric-label">Bajo Stock</div>
                                <div class="metric-change warning">Requiere atención</div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Precisión Pronóstico</h3>
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">92%</div>
                                <div class="metric-label">Este Mes</div>
                                <div class="metric-change positive">+3%</div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Última Ejecución</h3>
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">2h</div>
                                <div class="metric-label">Hace</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="MRPModule.runMRP()">
                                        Ejecutar Ahora
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mrp-alerts">
                        <h3>Alertas MRP</h3>
                        <div class="alerts-list">
                            <div class="alert-item critical">
                                <i class="fas fa-exclamation-circle"></i>
                                <div class="alert-content">
                                    <span class="alert-title">Stock crítico detectado</span>
                                    <span class="alert-description">Item COMP-001 tiene stock por debajo del mínimo</span>
                                </div>
                                <span class="alert-time">Hace 30 min</span>
                            </div>
                            <div class="alert-item warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <div class="alert-content">
                                    <span class="alert-title">Retraso en entrega</span>
                                    <span class="alert-description">Orden de compra PO-123 retrasada 3 días</span>
                                </div>
                                <span class="alert-time">Hace 1 hora</span>
                            </div>
                            <div class="alert-item info">
                                <i class="fas fa-info-circle"></i>
                                <div class="alert-content">
                                    <span class="alert-title">Pronóstico actualizado</span>
                                    <span class="alert-description">Demanda proyectada para Q2 disponible</span>
                                </div>
                                <span class="alert-time">Hace 2 horas</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mrp-summary">
                        <h3>Resumen de Planificación</h3>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="summary-label">Items Planificados</span>
                                <span class="summary-value">247</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Órdenes de Compra Sugeridas</span>
                                <span class="summary-value">12</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Órdenes de Producción Sugeridas</span>
                                <span class="summary-value">6</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Valor Total Planificado</span>
                                <span class="summary-value">$45,230</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Cargar planificación
     */
    function loadPlanning(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-calendar-alt"></i> Planificación de Materiales</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="MRPModule.runMRP()">
                            <i class="fas fa-play"></i>
                            Ejecutar MRP
                        </button>
                        <button class="btn btn-secondary" onclick="MRPModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="planning-controls">
                        <div class="control-group">
                            <label>Horizonte de Planificación:</label>
                            <select id="planning-horizon">
                                <option value="30">30 días</option>
                                <option value="60" selected>60 días</option>
                                <option value="90">90 días</option>
                                <option value="180">180 días</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label>Tipo de Planificación:</label>
                            <select id="planning-type">
                                <option value="regenerative" selected>Regenerativa</option>
                                <option value="net-change">Cambio Neto</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <button class="btn btn-primary" onclick="MRPModule.generatePlan()">
                                <i class="fas fa-cogs"></i>
                                Generar Plan
                            </button>
                        </div>
                    </div>
                    
                    <div class="data-table-container">
                        <div class="table-controls">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Buscar items..." id="planning-search">
                            </div>
                            <div class="table-actions">
                                <select id="planning-filter">
                                    <option value="">Todos los Items</option>
                                    <option value="critical">Críticos</option>
                                    <option value="shortage">Con Faltante</option>
                                    <option value="excess">Con Exceso</option>
                                </select>
                                <button class="btn btn-secondary" onclick="MRPModule.exportPlan()">
                                    <i class="fas fa-download"></i>
                                    Exportar Plan
                                </button>
                            </div>
                        </div>
                        
                        <div class="data-table-wrapper">
                            <table class="data-table" id="planning-table">
                                <thead>
                                    <tr>
                                        <th data-sort="itemCode">Código Item</th>
                                        <th data-sort="description">Descripción</th>
                                        <th data-sort="currentStock">Stock Actual</th>
                                        <th data-sort="projectedStock">Stock Proyectado</th>
                                        <th data-sort="plannedOrders">Órdenes Planificadas</th>
                                        <th data-sort="suggestedAction">Acción Sugerida</th>
                                        <th data-sort="priority">Prioridad</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody id="planning-tbody">
                                    <tr>
                                        <td colspan="8" class="text-center">Cargando plan de materiales...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="table-pagination">
                            <div class="pagination-info">
                                Mostrando <span id="showing-start">0</span> - <span id="showing-end">0</span> de <span id="total-records">0</span> registros
                            </div>
                            <div class="pagination-controls">
                                <select id="page-size">
                                    <option value="25">25</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="250">250</option>
                                    <option value="500">500</option>
                                </select>
                                <button class="btn btn-sm" id="prev-page" disabled>Anterior</button>
                                <span id="page-info">Página 1 de 1</span>
                                <button class="btn btn-sm" id="next-page" disabled>Siguiente</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Cargar datos de planificación
        loadPlanningData();
    }

    /**
     * Cargar pronósticos
     */
    function loadForecasting(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-chart-line"></i> Pronósticos de Demanda</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="MRPModule.generateForecast()">
                            <i class="fas fa-magic"></i>
                            Generar Pronóstico
                        </button>
                        <button class="btn btn-secondary" onclick="MRPModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Pronósticos de demanda se cargarán aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar recomendaciones
     */
    function loadRecommendations(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-lightbulb"></i> Recomendaciones MRP</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="MRPModule.acceptAllRecommendations()">
                            <i class="fas fa-check-double"></i>
                            Aceptar Todas
                        </button>
                        <button class="btn btn-secondary" onclick="MRPModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Recomendaciones MRP se cargarán aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Ejecutar MRP
     */
    function runMRP() {
        console.log('Ejecutando MRP...');
        showMessage('Ejecutando planificación de materiales...', 'info');
        
        // Simular ejecución de MRP
        setTimeout(() => {
            showMessage('MRP ejecutado exitosamente', 'success');
        }, 2000);
    }

    /**
     * Generar plan
     */
    function generatePlan() {
        console.log('Generando plan de materiales...');
        showMessage('Generando plan de materiales...', 'info');
        
        // Simular generación de plan
        setTimeout(() => {
            loadPlanningData();
            showMessage('Plan de materiales generado', 'success');
        }, 1500);
    }

    /**
     * Cargar datos de planificación (simulado)
     */
    async function loadPlanningData() {
        // Simular datos de planificación
        const mockData = [
            {
                itemCode: 'COMP-001',
                description: 'Componente A',
                currentStock: 50,
                projectedStock: 25,
                plannedOrders: 100,
                suggestedAction: 'Comprar',
                priority: 'high'
            },
            {
                itemCode: 'COMP-002',
                description: 'Componente B',
                currentStock: 200,
                projectedStock: 180,
                plannedOrders: 0,
                suggestedAction: 'Ninguna',
                priority: 'low'
            }
        ];

        const tbody = document.getElementById('planning-tbody');
        if (tbody) {
            tbody.innerHTML = mockData.map(item => `
                <tr ondblclick="MRPModule.viewItemPlan('${item.itemCode}')">
                    <td>${item.itemCode}</td>
                    <td>${item.description}</td>
                    <td>${item.currentStock}</td>
                    <td>${item.projectedStock}</td>
                    <td>${item.plannedOrders}</td>
                    <td><span class="action-badge ${item.suggestedAction.toLowerCase()}">${item.suggestedAction}</span></td>
                    <td><span class="priority-badge ${item.priority}">${getPriorityLabel(item.priority)}</span></td>
                    <td>
                        <button class="btn btn-sm" onclick="MRPModule.viewItemPlan('${item.itemCode}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm" onclick="MRPModule.acceptRecommendation('${item.itemCode}')">
                            <i class="fas fa-check"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }
    }

    /**
     * Obtener etiqueta de prioridad
     */
    function getPriorityLabel(priority) {
        const labels = {
            'low': 'Baja',
            'medium': 'Media',
            'high': 'Alta',
            'critical': 'Crítica'
        };
        return labels[priority] || priority;
    }

    /**
     * Ver plan de item
     */
    function viewItemPlan(itemCode) {
        console.log('Viendo plan de item:', itemCode);
        showMessage(`Viendo plan para item ${itemCode}`, 'info');
    }

    /**
     * Aceptar recomendación
     */
    function acceptRecommendation(itemCode) {
        console.log('Aceptando recomendación para:', itemCode);
        showMessage(`Recomendación aceptada para ${itemCode}`, 'success');
    }

    /**
     * Aceptar todas las recomendaciones
     */
    function acceptAllRecommendations() {
        console.log('Aceptando todas las recomendaciones...');
        showMessage('Todas las recomendaciones han sido aceptadas', 'success');
    }

    /**
     * Generar pronóstico
     */
    function generateForecast() {
        console.log('Generando pronóstico...');
        showMessage('Función de pronóstico no implementada aún', 'info');
    }

    /**
     * Exportar plan
     */
    function exportPlan() {
        console.log('Exportando plan...');
        showMessage('Función de exportación no implementada aún', 'info');
    }

    /**
     * Mostrar mensaje
     */
    function showMessage(message, type = 'info') {
        if (window.App && window.App.showMessage) {
            window.App.showMessage(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    // API pública
    return {
        init,
        load,
        runMRP,
        generatePlan,
        viewItemPlan,
        acceptRecommendation,
        acceptAllRecommendations,
        generateForecast,
        exportPlan
    };
})();

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    MRPModule.init();
});
