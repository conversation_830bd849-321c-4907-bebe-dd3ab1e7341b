/**
 * <PERSON><PERSON><PERSON><PERSON> de Servicio
 * Maneja funcionalidad relacionada con llamadas de servicio y contratos
 */
window.ServiceModule = (function() {
    'use strict';

    let currentView = 'list';
    let currentData = [];

    /**
     * Inicializar módulo de servicio
     */
    function init() {
        console.log('Inicializando módulo de Servicio...');
    }

    /**
     * Cargar contenido del módulo
     */
    function load(container, params = {}) {
        if (!container) return;

        const view = params.view || 'dashboard';
        
        switch (view) {
            case 'dashboard':
                loadDashboard(container);
                break;
            case 'service-calls':
                loadServiceCalls(container);
                break;
            case 'contracts':
                loadContracts(container);
                break;
            case 'knowledge-base':
                loadKnowledgeBase(container);
                break;
            default:
                loadDashboard(container);
        }
    }

    /**
     * Cargar dashboard de servicio
     */
    function loadDashboard(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-tools"></i> Servicio</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="ServiceModule.createServiceCall()">
                            <i class="fas fa-plus"></i>
                            Nueva Llamada de Servicio
                        </button>
                        <button class="btn btn-secondary" onclick="ServiceModule.load(document.getElementById('content-area'), {view: 'contracts'})">
                            <i class="fas fa-file-contract"></i>
                            Contratos
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Llamadas Abiertas</h3>
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">12</div>
                                <div class="metric-label">Pendientes</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="ServiceModule.load(document.getElementById('content-area'), {view: 'service-calls'})">
                                        Ver Todas
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Contratos Activos</h3>
                                <i class="fas fa-file-contract"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">28</div>
                                <div class="metric-label">Vigentes</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="ServiceModule.load(document.getElementById('content-area'), {view: 'contracts'})">
                                        Ver Todos
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Tiempo Promedio</h3>
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">4.2h</div>
                                <div class="metric-label">Resolución</div>
                                <div class="metric-change positive">-0.5h</div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Satisfacción</h3>
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">4.8</div>
                                <div class="metric-label">de 5.0</div>
                                <div class="metric-change positive">+0.2</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="service-priority-calls">
                        <h3>Llamadas de Alta Prioridad</h3>
                        <div class="priority-calls-list">
                            <div class="priority-call-item urgent">
                                <div class="call-info">
                                    <span class="call-number">SC-001</span>
                                    <span class="call-subject">Falla crítica en servidor</span>
                                    <span class="call-customer">Cliente ABC Corp</span>
                                </div>
                                <div class="call-status">
                                    <span class="priority-badge urgent">Urgente</span>
                                    <span class="time-elapsed">2h 30m</span>
                                </div>
                            </div>
                            <div class="priority-call-item high">
                                <div class="call-info">
                                    <span class="call-number">SC-002</span>
                                    <span class="call-subject">Problema de conectividad</span>
                                    <span class="call-customer">Cliente XYZ Ltd</span>
                                </div>
                                <div class="call-status">
                                    <span class="priority-badge high">Alta</span>
                                    <span class="time-elapsed">1h 15m</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recent-activity">
                        <h3>Actividad Reciente</h3>
                        <div class="activity-list">
                            <div class="activity-item">
                                <i class="fas fa-check text-success"></i>
                                <span>Llamada SC-003 cerrada exitosamente</span>
                                <span class="activity-time">Hace 1 hora</span>
                            </div>
                            <div class="activity-item">
                                <i class="fas fa-user text-primary"></i>
                                <span>Técnico asignado a SC-004</span>
                                <span class="activity-time">Hace 2 horas</span>
                            </div>
                            <div class="activity-item">
                                <i class="fas fa-plus text-info"></i>
                                <span>Nueva llamada SC-005 creada</span>
                                <span class="activity-time">Hace 3 horas</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Cargar llamadas de servicio
     */
    function loadServiceCalls(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-phone"></i> Llamadas de Servicio</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="ServiceModule.createServiceCall()">
                            <i class="fas fa-plus"></i>
                            Nueva Llamada
                        </button>
                        <button class="btn btn-secondary" onclick="ServiceModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="data-table-container">
                        <div class="table-controls">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Buscar llamadas..." id="service-search">
                            </div>
                            <div class="table-actions">
                                <select id="service-status-filter">
                                    <option value="">Todos los Estados</option>
                                    <option value="open">Abierta</option>
                                    <option value="assigned">Asignada</option>
                                    <option value="in-progress">En Progreso</option>
                                    <option value="resolved">Resuelta</option>
                                    <option value="closed">Cerrada</option>
                                </select>
                                <select id="service-priority-filter">
                                    <option value="">Todas las Prioridades</option>
                                    <option value="low">Baja</option>
                                    <option value="medium">Media</option>
                                    <option value="high">Alta</option>
                                    <option value="urgent">Urgente</option>
                                </select>
                                <button class="btn btn-secondary" onclick="ServiceModule.exportData()">
                                    <i class="fas fa-download"></i>
                                    Exportar
                                </button>
                            </div>
                        </div>
                        
                        <div class="data-table-wrapper">
                            <table class="data-table" id="service-calls-table">
                                <thead>
                                    <tr>
                                        <th data-sort="callNumber">Número</th>
                                        <th data-sort="subject">Asunto</th>
                                        <th data-sort="customer">Cliente</th>
                                        <th data-sort="priority">Prioridad</th>
                                        <th data-sort="status">Estado</th>
                                        <th data-sort="assignedTo">Asignado a</th>
                                        <th data-sort="createdDate">Fecha Creación</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody id="service-calls-tbody">
                                    <tr>
                                        <td colspan="8" class="text-center">Cargando llamadas de servicio...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="table-pagination">
                            <div class="pagination-info">
                                Mostrando <span id="showing-start">0</span> - <span id="showing-end">0</span> de <span id="total-records">0</span> registros
                            </div>
                            <div class="pagination-controls">
                                <select id="page-size">
                                    <option value="25">25</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="250">250</option>
                                    <option value="500">500</option>
                                </select>
                                <button class="btn btn-sm" id="prev-page" disabled>Anterior</button>
                                <span id="page-info">Página 1 de 1</span>
                                <button class="btn btn-sm" id="next-page" disabled>Siguiente</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Cargar datos de llamadas de servicio
        loadServiceCallsData();
    }

    /**
     * Crear nueva llamada de servicio
     */
    function createServiceCall() {
        console.log('Creando nueva llamada de servicio...');
        showMessage('Función de nueva llamada de servicio no implementada aún', 'info');
    }

    /**
     * Cargar contratos
     */
    function loadContracts(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-file-contract"></i> Contratos de Servicio</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="ServiceModule.createContract()">
                            <i class="fas fa-plus"></i>
                            Nuevo Contrato
                        </button>
                        <button class="btn btn-secondary" onclick="ServiceModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Contratos de servicio se cargarán aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar base de conocimiento
     */
    function loadKnowledgeBase(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-book"></i> Base de Conocimiento</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="ServiceModule.createArticle()">
                            <i class="fas fa-plus"></i>
                            Nuevo Artículo
                        </button>
                        <button class="btn btn-secondary" onclick="ServiceModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Base de conocimiento se cargará aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar datos de llamadas de servicio (simulado)
     */
    async function loadServiceCallsData() {
        // Simular datos de llamadas de servicio
        const mockData = [
            {
                callNumber: 'SC-001',
                subject: 'Falla crítica en servidor',
                customer: 'ABC Corp',
                priority: 'urgent',
                status: 'assigned',
                assignedTo: 'Juan Pérez',
                createdDate: '2024-01-15'
            },
            {
                callNumber: 'SC-002',
                subject: 'Problema de conectividad',
                customer: 'XYZ Ltd',
                priority: 'high',
                status: 'in-progress',
                assignedTo: 'María García',
                createdDate: '2024-01-14'
            }
        ];

        const tbody = document.getElementById('service-calls-tbody');
        if (tbody) {
            tbody.innerHTML = mockData.map(call => `
                <tr ondblclick="ServiceModule.viewServiceCall('${call.callNumber}')">
                    <td>${call.callNumber}</td>
                    <td>${call.subject}</td>
                    <td>${call.customer}</td>
                    <td><span class="priority-badge ${call.priority}">${getPriorityLabel(call.priority)}</span></td>
                    <td><span class="status-badge ${call.status}">${getStatusLabel(call.status)}</span></td>
                    <td>${call.assignedTo}</td>
                    <td>${call.createdDate}</td>
                    <td>
                        <button class="btn btn-sm" onclick="ServiceModule.viewServiceCall('${call.callNumber}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm" onclick="ServiceModule.editServiceCall('${call.callNumber}')">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }
    }

    /**
     * Obtener etiqueta de prioridad
     */
    function getPriorityLabel(priority) {
        const labels = {
            'low': 'Baja',
            'medium': 'Media',
            'high': 'Alta',
            'urgent': 'Urgente'
        };
        return labels[priority] || priority;
    }

    /**
     * Obtener etiqueta de estado
     */
    function getStatusLabel(status) {
        const labels = {
            'open': 'Abierta',
            'assigned': 'Asignada',
            'in-progress': 'En Progreso',
            'resolved': 'Resuelta',
            'closed': 'Cerrada'
        };
        return labels[status] || status;
    }

    /**
     * Ver llamada de servicio
     */
    function viewServiceCall(callNumber) {
        console.log('Viendo llamada de servicio:', callNumber);
        showMessage(`Viendo llamada de servicio ${callNumber}`, 'info');
    }

    /**
     * Editar llamada de servicio
     */
    function editServiceCall(callNumber) {
        console.log('Editando llamada de servicio:', callNumber);
        showMessage(`Editando llamada de servicio ${callNumber}`, 'info');
    }

    /**
     * Crear nuevo contrato
     */
    function createContract() {
        console.log('Creando nuevo contrato...');
        showMessage('Función de nuevo contrato no implementada aún', 'info');
    }

    /**
     * Crear nuevo artículo
     */
    function createArticle() {
        console.log('Creando nuevo artículo...');
        showMessage('Función de nuevo artículo no implementada aún', 'info');
    }

    /**
     * Exportar datos
     */
    function exportData() {
        console.log('Exportando datos de servicio...');
        showMessage('Función de exportación no implementada aún', 'info');
    }

    /**
     * Mostrar mensaje
     */
    function showMessage(message, type = 'info') {
        if (window.App && window.App.showMessage) {
            window.App.showMessage(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    // API pública
    return {
        init,
        load,
        createServiceCall,
        viewServiceCall,
        editServiceCall,
        createContract,
        createArticle,
        exportData
    };
})();

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    ServiceModule.init();
});
