/**
 * Módulo de Gestión de Proyectos
 * Maneja funcionalidad relacionada con proyectos y tareas
 */
window.ProjectsModule = (function() {
    'use strict';

    /**
     * Inicializar módulo de proyectos
     */
    function init() {
        console.log('Inicializando módulo de Proyectos...');
    }

    /**
     * Cargar contenido del módulo
     */
    function load(container, params = {}) {
        if (!container) return;

        const view = params.view || 'dashboard';
        
        switch (view) {
            case 'dashboard':
                loadDashboard(container);
                break;
            case 'projects':
                loadProjects(container);
                break;
            case 'tasks':
                loadTasks(container);
                break;
            case 'time-tracking':
                loadTimeTracking(container);
                break;
            default:
                loadDashboard(container);
        }
    }

    /**
     * Cargar dashboard de proyectos
     */
    function loadDashboard(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-tasks"></i> Gestión de Proyectos</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="ProjectsModule.createProject()">
                            <i class="fas fa-plus"></i>
                            Nuevo Proyecto
                        </button>
                        <button class="btn btn-secondary" onclick="ProjectsModule.load(document.getElementById('content-area'), {view: 'tasks'})">
                            <i class="fas fa-list"></i>
                            Tareas
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Proyectos Activos</h3>
                                <i class="fas fa-project-diagram"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">12</div>
                                <div class="metric-label">En Progreso</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="ProjectsModule.load(document.getElementById('content-area'), {view: 'projects'})">
                                        Ver Todos
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Tareas Pendientes</h3>
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">47</div>
                                <div class="metric-label">Por Completar</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="ProjectsModule.load(document.getElementById('content-area'), {view: 'tasks'})">
                                        Ver Todas
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Progreso General</h3>
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">68%</div>
                                <div class="metric-label">Completado</div>
                                <div class="metric-change positive">+5%</div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Horas Registradas</h3>
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">324h</div>
                                <div class="metric-label">Este Mes</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="ProjectsModule.load(document.getElementById('content-area'), {view: 'time-tracking'})">
                                        Ver Detalle
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="projects-overview">
                        <h3>Proyectos Críticos</h3>
                        <div class="critical-projects">
                            <div class="project-item urgent">
                                <div class="project-info">
                                    <span class="project-name">Implementación ERP Cliente A</span>
                                    <span class="project-progress">85% completado</span>
                                </div>
                                <div class="project-status">
                                    <span class="status-badge urgent">Retrasado</span>
                                    <span class="due-date">Vence: 15/02/2024</span>
                                </div>
                            </div>
                            <div class="project-item warning">
                                <div class="project-info">
                                    <span class="project-name">Migración Base de Datos</span>
                                    <span class="project-progress">45% completado</span>
                                </div>
                                <div class="project-status">
                                    <span class="status-badge warning">En Riesgo</span>
                                    <span class="due-date">Vence: 28/02/2024</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recent-activity">
                        <h3>Actividad Reciente</h3>
                        <div class="activity-list">
                            <div class="activity-item">
                                <i class="fas fa-check text-success"></i>
                                <span>Tarea "Configuración inicial" completada</span>
                                <span class="activity-time">Hace 1 hora</span>
                            </div>
                            <div class="activity-item">
                                <i class="fas fa-plus text-primary"></i>
                                <span>Nueva tarea "Testing módulo ventas" creada</span>
                                <span class="activity-time">Hace 2 horas</span>
                            </div>
                            <div class="activity-item">
                                <i class="fas fa-clock text-info"></i>
                                <span>4.5 horas registradas en Proyecto Alpha</span>
                                <span class="activity-time">Hace 3 horas</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Cargar proyectos
     */
    function loadProjects(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-project-diagram"></i> Proyectos</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="ProjectsModule.createProject()">
                            <i class="fas fa-plus"></i>
                            Nuevo Proyecto
                        </button>
                        <button class="btn btn-secondary" onclick="ProjectsModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="data-table-container">
                        <div class="table-controls">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Buscar proyectos..." id="projects-search">
                            </div>
                            <div class="table-actions">
                                <select id="projects-status-filter">
                                    <option value="">Todos los Estados</option>
                                    <option value="planning">Planificación</option>
                                    <option value="active">Activo</option>
                                    <option value="on-hold">En Pausa</option>
                                    <option value="completed">Completado</option>
                                    <option value="cancelled">Cancelado</option>
                                </select>
                                <button class="btn btn-secondary" onclick="ProjectsModule.exportProjects()">
                                    <i class="fas fa-download"></i>
                                    Exportar
                                </button>
                            </div>
                        </div>
                        
                        <div class="data-table-wrapper">
                            <table class="data-table" id="projects-table">
                                <thead>
                                    <tr>
                                        <th data-sort="name">Nombre</th>
                                        <th data-sort="customer">Cliente</th>
                                        <th data-sort="manager">Gerente</th>
                                        <th data-sort="progress">Progreso</th>
                                        <th data-sort="status">Estado</th>
                                        <th data-sort="startDate">Fecha Inicio</th>
                                        <th data-sort="dueDate">Fecha Fin</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody id="projects-tbody">
                                    <tr>
                                        <td colspan="8" class="text-center">Cargando proyectos...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="table-pagination">
                            <div class="pagination-info">
                                Mostrando <span id="showing-start">0</span> - <span id="showing-end">0</span> de <span id="total-records">0</span> registros
                            </div>
                            <div class="pagination-controls">
                                <select id="page-size">
                                    <option value="25">25</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="250">250</option>
                                    <option value="500">500</option>
                                </select>
                                <button class="btn btn-sm" id="prev-page" disabled>Anterior</button>
                                <span id="page-info">Página 1 de 1</span>
                                <button class="btn btn-sm" id="next-page" disabled>Siguiente</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Cargar datos de proyectos
        loadProjectsData();
    }

    /**
     * Cargar tareas
     */
    function loadTasks(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-list"></i> Tareas</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="ProjectsModule.createTask()">
                            <i class="fas fa-plus"></i>
                            Nueva Tarea
                        </button>
                        <button class="btn btn-secondary" onclick="ProjectsModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Tareas se cargarán aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar seguimiento de tiempo
     */
    function loadTimeTracking(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-clock"></i> Seguimiento de Tiempo</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="ProjectsModule.startTimer()">
                            <i class="fas fa-play"></i>
                            Iniciar Timer
                        </button>
                        <button class="btn btn-secondary" onclick="ProjectsModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Seguimiento de tiempo se cargará aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar datos de proyectos (simulado)
     */
    async function loadProjectsData() {
        const mockData = [
            {
                name: 'Implementación ERP Cliente A',
                customer: 'ABC Corp',
                manager: 'Juan Pérez',
                progress: 85,
                status: 'active',
                startDate: '2024-01-01',
                dueDate: '2024-02-15'
            },
            {
                name: 'Migración Base de Datos',
                customer: 'XYZ Ltd',
                manager: 'María García',
                progress: 45,
                status: 'active',
                startDate: '2024-01-15',
                dueDate: '2024-02-28'
            }
        ];

        const tbody = document.getElementById('projects-tbody');
        if (tbody) {
            tbody.innerHTML = mockData.map(project => `
                <tr ondblclick="ProjectsModule.viewProject('${project.name}')">
                    <td>${project.name}</td>
                    <td>${project.customer}</td>
                    <td>${project.manager}</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${project.progress}%"></div>
                            <span class="progress-text">${project.progress}%</span>
                        </div>
                    </td>
                    <td><span class="status-badge ${project.status}">${getStatusLabel(project.status)}</span></td>
                    <td>${project.startDate}</td>
                    <td>${project.dueDate}</td>
                    <td>
                        <button class="btn btn-sm" onclick="ProjectsModule.viewProject('${project.name}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm" onclick="ProjectsModule.editProject('${project.name}')">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }
    }

    /**
     * Obtener etiqueta de estado
     */
    function getStatusLabel(status) {
        const labels = {
            'planning': 'Planificación',
            'active': 'Activo',
            'on-hold': 'En Pausa',
            'completed': 'Completado',
            'cancelled': 'Cancelado'
        };
        return labels[status] || status;
    }

    /**
     * Crear nuevo proyecto
     */
    function createProject() {
        console.log('Creando nuevo proyecto...');
        showMessage('Función de nuevo proyecto no implementada aún', 'info');
    }

    /**
     * Ver proyecto
     */
    function viewProject(name) {
        console.log('Viendo proyecto:', name);
        showMessage(`Viendo proyecto: ${name}`, 'info');
    }

    /**
     * Editar proyecto
     */
    function editProject(name) {
        console.log('Editando proyecto:', name);
        showMessage(`Editando proyecto: ${name}`, 'info');
    }

    /**
     * Crear nueva tarea
     */
    function createTask() {
        console.log('Creando nueva tarea...');
        showMessage('Función de nueva tarea no implementada aún', 'info');
    }

    /**
     * Iniciar timer
     */
    function startTimer() {
        console.log('Iniciando timer...');
        showMessage('Función de timer no implementada aún', 'info');
    }

    /**
     * Exportar proyectos
     */
    function exportProjects() {
        console.log('Exportando proyectos...');
        showMessage('Función de exportación no implementada aún', 'info');
    }

    /**
     * Mostrar mensaje
     */
    function showMessage(message, type = 'info') {
        if (window.App && window.App.showMessage) {
            window.App.showMessage(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    // API pública
    return {
        init,
        load,
        createProject,
        viewProject,
        editProject,
        createTask,
        startTimer,
        exportProjects
    };
})();

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    ProjectsModule.init();
});
