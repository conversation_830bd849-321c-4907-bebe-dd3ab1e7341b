/**
 * <PERSON><PERSON><PERSON><PERSON> de CRM (Customer Relationship Management)
 * Maneja funcionalidad relacionada con gestión de relaciones con clientes
 */
window.CRMModule = (function() {
    'use strict';

    let currentView = 'list';
    let currentData = [];

    /**
     * Inicializar módulo de CRM
     */
    function init() {
        console.log('Inicializando módulo de CRM...');
    }

    /**
     * Cargar contenido del módulo
     */
    function load(container, params = {}) {
        if (!container) return;

        const view = params.view || 'dashboard';
        
        switch (view) {
            case 'dashboard':
                loadDashboard(container);
                break;
            case 'opportunities':
                loadOpportunities(container);
                break;
            case 'activities':
                loadActivities(container);
                break;
            case 'campaigns':
                loadCampaigns(container);
                break;
            case 'leads':
                loadLeads(container);
                break;
            default:
                loadDashboard(container);
        }
    }

    /**
     * Cargar dashboard de CRM
     */
    function loadDashboard(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-handshake"></i> CRM - Gestión de Relaciones</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="CRMModule.createOpportunity()">
                            <i class="fas fa-plus"></i>
                            Nueva Oportunidad
                        </button>
                        <button class="btn btn-secondary" onclick="CRMModule.load(document.getElementById('content-area'), {view: 'activities'})">
                            <i class="fas fa-calendar"></i>
                            Actividades
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Oportunidades Abiertas</h3>
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">24</div>
                                <div class="metric-label">Activas</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="CRMModule.load(document.getElementById('content-area'), {view: 'opportunities'})">
                                        Ver Todas
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Valor Pipeline</h3>
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">$485K</div>
                                <div class="metric-label">Total</div>
                                <div class="metric-change positive">+12%</div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Tasa de Conversión</h3>
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">28%</div>
                                <div class="metric-label">Este Mes</div>
                                <div class="metric-change positive">+3%</div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Actividades Hoy</h3>
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">8</div>
                                <div class="metric-label">Pendientes</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="CRMModule.load(document.getElementById('content-area'), {view: 'activities'})">
                                        Ver Todas
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="crm-pipeline">
                        <h3>Pipeline de Ventas</h3>
                        <div class="pipeline-stages">
                            <div class="pipeline-stage">
                                <div class="stage-header">
                                    <span class="stage-name">Prospecto</span>
                                    <span class="stage-count">12</span>
                                </div>
                                <div class="stage-value">$125K</div>
                            </div>
                            <div class="pipeline-stage">
                                <div class="stage-header">
                                    <span class="stage-name">Calificado</span>
                                    <span class="stage-count">8</span>
                                </div>
                                <div class="stage-value">$180K</div>
                            </div>
                            <div class="pipeline-stage">
                                <div class="stage-header">
                                    <span class="stage-name">Propuesta</span>
                                    <span class="stage-count">4</span>
                                </div>
                                <div class="stage-value">$120K</div>
                            </div>
                            <div class="pipeline-stage">
                                <div class="stage-header">
                                    <span class="stage-name">Negociación</span>
                                    <span class="stage-count">3</span>
                                </div>
                                <div class="stage-value">$60K</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recent-activities">
                        <h3>Actividades Recientes</h3>
                        <div class="activities-list">
                            <div class="activity-item">
                                <i class="fas fa-phone text-primary"></i>
                                <div class="activity-content">
                                    <span class="activity-title">Llamada con ABC Corp</span>
                                    <span class="activity-description">Seguimiento de propuesta comercial</span>
                                    <span class="activity-contact">Juan Pérez</span>
                                </div>
                                <span class="activity-time">Hace 2 horas</span>
                            </div>
                            <div class="activity-item">
                                <i class="fas fa-envelope text-info"></i>
                                <div class="activity-content">
                                    <span class="activity-title">Email enviado a XYZ Ltd</span>
                                    <span class="activity-description">Propuesta técnica adjunta</span>
                                    <span class="activity-contact">María García</span>
                                </div>
                                <span class="activity-time">Hace 4 horas</span>
                            </div>
                            <div class="activity-item">
                                <i class="fas fa-calendar text-success"></i>
                                <div class="activity-content">
                                    <span class="activity-title">Reunión programada</span>
                                    <span class="activity-description">Demo de producto - DEF Inc</span>
                                    <span class="activity-contact">Carlos López</span>
                                </div>
                                <span class="activity-time">Mañana 10:00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Cargar oportunidades
     */
    function loadOpportunities(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-bullseye"></i> Oportunidades de Venta</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="CRMModule.createOpportunity()">
                            <i class="fas fa-plus"></i>
                            Nueva Oportunidad
                        </button>
                        <button class="btn btn-secondary" onclick="CRMModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="data-table-container">
                        <div class="table-controls">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Buscar oportunidades..." id="opportunities-search">
                            </div>
                            <div class="table-actions">
                                <select id="opportunities-stage-filter">
                                    <option value="">Todas las Etapas</option>
                                    <option value="prospect">Prospecto</option>
                                    <option value="qualified">Calificado</option>
                                    <option value="proposal">Propuesta</option>
                                    <option value="negotiation">Negociación</option>
                                    <option value="closed-won">Ganada</option>
                                    <option value="closed-lost">Perdida</option>
                                </select>
                                <select id="opportunities-owner-filter">
                                    <option value="">Todos los Vendedores</option>
                                    <option value="juan">Juan Pérez</option>
                                    <option value="maria">María García</option>
                                    <option value="carlos">Carlos López</option>
                                </select>
                                <button class="btn btn-secondary" onclick="CRMModule.exportOpportunities()">
                                    <i class="fas fa-download"></i>
                                    Exportar
                                </button>
                            </div>
                        </div>
                        
                        <div class="data-table-wrapper">
                            <table class="data-table" id="opportunities-table">
                                <thead>
                                    <tr>
                                        <th data-sort="name">Nombre</th>
                                        <th data-sort="customer">Cliente</th>
                                        <th data-sort="stage">Etapa</th>
                                        <th data-sort="value">Valor</th>
                                        <th data-sort="probability">Probabilidad</th>
                                        <th data-sort="owner">Vendedor</th>
                                        <th data-sort="closeDate">Fecha Cierre</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody id="opportunities-tbody">
                                    <tr>
                                        <td colspan="8" class="text-center">Cargando oportunidades...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="table-pagination">
                            <div class="pagination-info">
                                Mostrando <span id="showing-start">0</span> - <span id="showing-end">0</span> de <span id="total-records">0</span> registros
                            </div>
                            <div class="pagination-controls">
                                <select id="page-size">
                                    <option value="25">25</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="250">250</option>
                                    <option value="500">500</option>
                                </select>
                                <button class="btn btn-sm" id="prev-page" disabled>Anterior</button>
                                <span id="page-info">Página 1 de 1</span>
                                <button class="btn btn-sm" id="next-page" disabled>Siguiente</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Cargar datos de oportunidades
        loadOpportunitiesData();
    }

    /**
     * Crear nueva oportunidad
     */
    function createOpportunity() {
        console.log('Creando nueva oportunidad...');
        showMessage('Función de nueva oportunidad no implementada aún', 'info');
    }

    /**
     * Cargar actividades
     */
    function loadActivities(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-calendar"></i> Actividades CRM</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="CRMModule.createActivity()">
                            <i class="fas fa-plus"></i>
                            Nueva Actividad
                        </button>
                        <button class="btn btn-secondary" onclick="CRMModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Actividades CRM se cargarán aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar campañas
     */
    function loadCampaigns(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-bullhorn"></i> Campañas de Marketing</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="CRMModule.createCampaign()">
                            <i class="fas fa-plus"></i>
                            Nueva Campaña
                        </button>
                        <button class="btn btn-secondary" onclick="CRMModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Campañas de marketing se cargarán aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar leads
     */
    function loadLeads(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-user-plus"></i> Leads</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="CRMModule.createLead()">
                            <i class="fas fa-plus"></i>
                            Nuevo Lead
                        </button>
                        <button class="btn btn-secondary" onclick="CRMModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Leads se cargarán aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar datos de oportunidades (simulado)
     */
    async function loadOpportunitiesData() {
        // Simular datos de oportunidades
        const mockData = [
            {
                name: 'Proyecto ERP ABC Corp',
                customer: 'ABC Corp',
                stage: 'proposal',
                value: 125000,
                probability: 75,
                owner: 'Juan Pérez',
                closeDate: '2024-02-15'
            },
            {
                name: 'Implementación CRM XYZ',
                customer: 'XYZ Ltd',
                stage: 'negotiation',
                value: 85000,
                probability: 60,
                owner: 'María García',
                closeDate: '2024-02-28'
            }
        ];

        const tbody = document.getElementById('opportunities-tbody');
        if (tbody) {
            tbody.innerHTML = mockData.map(opp => `
                <tr ondblclick="CRMModule.viewOpportunity('${opp.name}')">
                    <td>${opp.name}</td>
                    <td>${opp.customer}</td>
                    <td><span class="stage-badge ${opp.stage}">${getStageLabel(opp.stage)}</span></td>
                    <td>$${opp.value.toLocaleString()}</td>
                    <td>${opp.probability}%</td>
                    <td>${opp.owner}</td>
                    <td>${opp.closeDate}</td>
                    <td>
                        <button class="btn btn-sm" onclick="CRMModule.viewOpportunity('${opp.name}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm" onclick="CRMModule.editOpportunity('${opp.name}')">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }
    }

    /**
     * Obtener etiqueta de etapa
     */
    function getStageLabel(stage) {
        const labels = {
            'prospect': 'Prospecto',
            'qualified': 'Calificado',
            'proposal': 'Propuesta',
            'negotiation': 'Negociación',
            'closed-won': 'Ganada',
            'closed-lost': 'Perdida'
        };
        return labels[stage] || stage;
    }

    /**
     * Ver oportunidad
     */
    function viewOpportunity(name) {
        console.log('Viendo oportunidad:', name);
        showMessage(`Viendo oportunidad: ${name}`, 'info');
    }

    /**
     * Editar oportunidad
     */
    function editOpportunity(name) {
        console.log('Editando oportunidad:', name);
        showMessage(`Editando oportunidad: ${name}`, 'info');
    }

    /**
     * Crear nueva actividad
     */
    function createActivity() {
        console.log('Creando nueva actividad...');
        showMessage('Función de nueva actividad no implementada aún', 'info');
    }

    /**
     * Crear nueva campaña
     */
    function createCampaign() {
        console.log('Creando nueva campaña...');
        showMessage('Función de nueva campaña no implementada aún', 'info');
    }

    /**
     * Crear nuevo lead
     */
    function createLead() {
        console.log('Creando nuevo lead...');
        showMessage('Función de nuevo lead no implementada aún', 'info');
    }

    /**
     * Exportar oportunidades
     */
    function exportOpportunities() {
        console.log('Exportando oportunidades...');
        showMessage('Función de exportación no implementada aún', 'info');
    }

    /**
     * Mostrar mensaje
     */
    function showMessage(message, type = 'info') {
        if (window.App && window.App.showMessage) {
            window.App.showMessage(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    // API pública
    return {
        init,
        load,
        createOpportunity,
        viewOpportunity,
        editOpportunity,
        createActivity,
        createCampaign,
        createLead,
        exportOpportunities
    };
})();

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    CRMModule.init();
});
