/**
 * Módulo de Activos Fijos
 * Maneja funcionalidad relacionada con activos fijos y depreciación
 */
window.AssetsModule = (function() {
    'use strict';

    /**
     * Inicializar módulo de activos fijos
     */
    function init() {
        console.log('Inicializando módulo de Activos Fijos...');
    }

    /**
     * Cargar contenido del módulo
     */
    function load(container, params = {}) {
        if (!container) return;

        const view = params.view || 'dashboard';
        
        switch (view) {
            case 'dashboard':
                loadDashboard(container);
                break;
            case 'assets':
                loadAssets(container);
                break;
            case 'depreciation':
                loadDepreciation(container);
                break;
            case 'maintenance':
                loadMaintenance(container);
                break;
            default:
                loadDashboard(container);
        }
    }

    /**
     * Cargar dashboard de activos fijos
     */
    function loadDashboard(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-building"></i> Activos Fijos</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="AssetsModule.createAsset()">
                            <i class="fas fa-plus"></i>
                            Nuevo Activo
                        </button>
                        <button class="btn btn-secondary" onclick="AssetsModule.load(document.getElementById('content-area'), {view: 'depreciation'})">
                            <i class="fas fa-chart-line"></i>
                            Depreciación
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Total Activos</h3>
                                <i class="fas fa-warehouse"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">156</div>
                                <div class="metric-label">Registrados</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="AssetsModule.load(document.getElementById('content-area'), {view: 'assets'})">
                                        Ver Todos
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Valor Total</h3>
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">$2.4M</div>
                                <div class="metric-label">Valor Libro</div>
                                <div class="metric-change negative">-$45K este mes</div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Depreciación Mensual</h3>
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">$45K</div>
                                <div class="metric-label">Este Mes</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="AssetsModule.load(document.getElementById('content-area'), {view: 'depreciation'})">
                                        Ver Detalle
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Mantenimientos</h3>
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="card-content">
                                <div class="metric-large">8</div>
                                <div class="metric-label">Programados</div>
                                <div class="metric-actions">
                                    <button class="btn btn-sm" onclick="AssetsModule.load(document.getElementById('content-area'), {view: 'maintenance'})">
                                        Ver Todos
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="assets-by-category">
                        <h3>Activos por Categoría</h3>
                        <div class="category-grid">
                            <div class="category-item">
                                <div class="category-icon">
                                    <i class="fas fa-desktop"></i>
                                </div>
                                <div class="category-info">
                                    <span class="category-name">Equipos de Cómputo</span>
                                    <span class="category-count">45 activos</span>
                                    <span class="category-value">$125K</span>
                                </div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon">
                                    <i class="fas fa-car"></i>
                                </div>
                                <div class="category-info">
                                    <span class="category-name">Vehículos</span>
                                    <span class="category-count">12 activos</span>
                                    <span class="category-value">$485K</span>
                                </div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon">
                                    <i class="fas fa-chair"></i>
                                </div>
                                <div class="category-info">
                                    <span class="category-name">Mobiliario</span>
                                    <span class="category-count">89 activos</span>
                                    <span class="category-value">$78K</span>
                                </div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div class="category-info">
                                    <span class="category-name">Maquinaria</span>
                                    <span class="category-count">10 activos</span>
                                    <span class="category-value">$1.7M</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="assets-alerts">
                        <h3>Alertas de Activos</h3>
                        <div class="alerts-list">
                            <div class="alert-item warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <div class="alert-content">
                                    <span class="alert-title">Mantenimiento Vencido</span>
                                    <span class="alert-description">Vehículo VH-001 requiere mantenimiento</span>
                                </div>
                                <span class="alert-time">Vencido hace 5 días</span>
                            </div>
                            <div class="alert-item info">
                                <i class="fas fa-calendar"></i>
                                <div class="alert-content">
                                    <span class="alert-title">Mantenimiento Programado</span>
                                    <span class="alert-description">Servidor SV-002 - Mantenimiento el 20/02/2024</span>
                                </div>
                                <span class="alert-time">En 5 días</span>
                            </div>
                            <div class="alert-item critical">
                                <i class="fas fa-exclamation-circle"></i>
                                <div class="alert-content">
                                    <span class="alert-title">Activo Totalmente Depreciado</span>
                                    <span class="alert-description">Equipo EQ-045 ha alcanzado depreciación total</span>
                                </div>
                                <span class="alert-time">Este mes</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Cargar activos
     */
    function loadAssets(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-warehouse"></i> Activos Fijos</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="AssetsModule.createAsset()">
                            <i class="fas fa-plus"></i>
                            Nuevo Activo
                        </button>
                        <button class="btn btn-secondary" onclick="AssetsModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <div class="data-table-container">
                        <div class="table-controls">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Buscar activos..." id="assets-search">
                            </div>
                            <div class="table-actions">
                                <select id="assets-category-filter">
                                    <option value="">Todas las Categorías</option>
                                    <option value="computers">Equipos de Cómputo</option>
                                    <option value="vehicles">Vehículos</option>
                                    <option value="furniture">Mobiliario</option>
                                    <option value="machinery">Maquinaria</option>
                                </select>
                                <select id="assets-status-filter">
                                    <option value="">Todos los Estados</option>
                                    <option value="active">Activo</option>
                                    <option value="maintenance">En Mantenimiento</option>
                                    <option value="retired">Retirado</option>
                                    <option value="disposed">Dado de Baja</option>
                                </select>
                                <button class="btn btn-secondary" onclick="AssetsModule.exportAssets()">
                                    <i class="fas fa-download"></i>
                                    Exportar
                                </button>
                            </div>
                        </div>
                        
                        <div class="data-table-wrapper">
                            <table class="data-table" id="assets-table">
                                <thead>
                                    <tr>
                                        <th data-sort="assetCode">Código</th>
                                        <th data-sort="description">Descripción</th>
                                        <th data-sort="category">Categoría</th>
                                        <th data-sort="acquisitionCost">Costo Adquisición</th>
                                        <th data-sort="bookValue">Valor Libro</th>
                                        <th data-sort="acquisitionDate">Fecha Adquisición</th>
                                        <th data-sort="status">Estado</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody id="assets-tbody">
                                    <tr>
                                        <td colspan="8" class="text-center">Cargando activos...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="table-pagination">
                            <div class="pagination-info">
                                Mostrando <span id="showing-start">0</span> - <span id="showing-end">0</span> de <span id="total-records">0</span> registros
                            </div>
                            <div class="pagination-controls">
                                <select id="page-size">
                                    <option value="25">25</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="250">250</option>
                                    <option value="500">500</option>
                                </select>
                                <button class="btn btn-sm" id="prev-page" disabled>Anterior</button>
                                <span id="page-info">Página 1 de 1</span>
                                <button class="btn btn-sm" id="next-page" disabled>Siguiente</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Cargar datos de activos
        loadAssetsData();
    }

    /**
     * Cargar depreciación
     */
    function loadDepreciation(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-chart-line"></i> Depreciación de Activos</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="AssetsModule.calculateDepreciation()">
                            <i class="fas fa-calculator"></i>
                            Calcular Depreciación
                        </button>
                        <button class="btn btn-secondary" onclick="AssetsModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Cálculo de depreciación se cargará aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar mantenimiento
     */
    function loadMaintenance(container) {
        container.innerHTML = `
            <div class="module-container">
                <div class="module-header">
                    <h1><i class="fas fa-tools"></i> Mantenimiento de Activos</h1>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="AssetsModule.scheduleMaintenance()">
                            <i class="fas fa-plus"></i>
                            Programar Mantenimiento
                        </button>
                        <button class="btn btn-secondary" onclick="AssetsModule.load(document.getElementById('content-area'), {view: 'dashboard'})">
                            <i class="fas fa-arrow-left"></i>
                            Volver
                        </button>
                    </div>
                </div>
                
                <div class="module-content">
                    <p>Programación de mantenimiento se cargará aquí.</p>
                    <p>Esta funcionalidad está en desarrollo.</p>
                </div>
            </div>
        `;
    }

    /**
     * Cargar datos de activos (simulado)
     */
    async function loadAssetsData() {
        const mockData = [
            {
                assetCode: 'EQ-001',
                description: 'Laptop Dell Inspiron 15',
                category: 'Equipos de Cómputo',
                acquisitionCost: 1200,
                bookValue: 800,
                acquisitionDate: '2023-01-15',
                status: 'active'
            },
            {
                assetCode: 'VH-001',
                description: 'Toyota Corolla 2022',
                category: 'Vehículos',
                acquisitionCost: 25000,
                bookValue: 22000,
                acquisitionDate: '2022-06-10',
                status: 'active'
            }
        ];

        const tbody = document.getElementById('assets-tbody');
        if (tbody) {
            tbody.innerHTML = mockData.map(asset => `
                <tr ondblclick="AssetsModule.viewAsset('${asset.assetCode}')">
                    <td>${asset.assetCode}</td>
                    <td>${asset.description}</td>
                    <td>${asset.category}</td>
                    <td>$${asset.acquisitionCost.toLocaleString()}</td>
                    <td>$${asset.bookValue.toLocaleString()}</td>
                    <td>${asset.acquisitionDate}</td>
                    <td><span class="status-badge ${asset.status}">${getStatusLabel(asset.status)}</span></td>
                    <td>
                        <button class="btn btn-sm" onclick="AssetsModule.viewAsset('${asset.assetCode}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm" onclick="AssetsModule.editAsset('${asset.assetCode}')">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }
    }

    /**
     * Obtener etiqueta de estado
     */
    function getStatusLabel(status) {
        const labels = {
            'active': 'Activo',
            'maintenance': 'En Mantenimiento',
            'retired': 'Retirado',
            'disposed': 'Dado de Baja'
        };
        return labels[status] || status;
    }

    /**
     * Crear nuevo activo
     */
    function createAsset() {
        console.log('Creando nuevo activo...');
        showMessage('Función de nuevo activo no implementada aún', 'info');
    }

    /**
     * Ver activo
     */
    function viewAsset(assetCode) {
        console.log('Viendo activo:', assetCode);
        showMessage(`Viendo activo ${assetCode}`, 'info');
    }

    /**
     * Editar activo
     */
    function editAsset(assetCode) {
        console.log('Editando activo:', assetCode);
        showMessage(`Editando activo ${assetCode}`, 'info');
    }

    /**
     * Calcular depreciación
     */
    function calculateDepreciation() {
        console.log('Calculando depreciación...');
        showMessage('Función de cálculo de depreciación no implementada aún', 'info');
    }

    /**
     * Programar mantenimiento
     */
    function scheduleMaintenance() {
        console.log('Programando mantenimiento...');
        showMessage('Función de programación de mantenimiento no implementada aún', 'info');
    }

    /**
     * Exportar activos
     */
    function exportAssets() {
        console.log('Exportando activos...');
        showMessage('Función de exportación no implementada aún', 'info');
    }

    /**
     * Mostrar mensaje
     */
    function showMessage(message, type = 'info') {
        if (window.App && window.App.showMessage) {
            window.App.showMessage(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    // API pública
    return {
        init,
        load,
        createAsset,
        viewAsset,
        editAsset,
        calculateDepreciation,
        scheduleMaintenance,
        exportAssets
    };
})();

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    AssetsModule.init();
});
