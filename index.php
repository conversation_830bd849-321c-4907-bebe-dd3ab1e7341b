<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cliente Web SAP Business One</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/login.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <link rel="stylesheet" href="assets/css/fondos-fijos.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Pantalla de Inicio de Sesión -->
    <div id="login-container" class="login-container active">
        <div class="login-form">
            <div class="login-header">
                <img src="assets/images/sap-logo.svg" alt="SAP" class="sap-logo">
                <h2>SAP Business One</h2>
                <p>Cliente Web</p>
            </div>
            
            <form id="login-form">
                <div class="form-group">
                    <label for="server">Servidor:</label>
                    <input type="text" id="server" name="server" value="https://192.168.2.249:50000/b1s/v2" required>
                </div>
                
                <div class="form-group">
                    <label for="database">Base de Datos:</label>
                    <div class="database-switch">
                        <input type="radio" id="db-dev" name="database" value="CAPA" checked>
                        <label for="db-dev" class="db-option dev">
                            <i class="fas fa-database"></i>
                            Desarrollo (CAPA)
                        </label>
                        
                        <input type="radio" id="db-prod" name="database" value="SBO_ECOM">
                        <label for="db-prod" class="db-option prod">
                            <i class="fas fa-server"></i>
                            Producción (SBO_ECOM)
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="username">Usuario:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Contraseña:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    Conectar
                </button>
                
                <div class="login-status" id="login-status"></div>
            </form>
        </div>
    </div>

    <!-- Aplicación Principal -->
    <div id="main-app" class="main-app">
        <!-- Encabezado -->
        <header class="app-header">
            <div class="header-left">
                <img src="assets/images/sap-logo.svg" alt="SAP" class="header-logo">
                <span class="company-name" id="company-name">SAP Business One</span>
            </div>
            
            <div class="header-center">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Buscar...">
                </div>
            </div>
            
            <div class="header-right">
                <div class="user-info-dropdown">
                    <div class="user-info" id="user-info-trigger">
                        <div class="user-details">
                            <span id="current-user">Usuario</span>
                            <span id="current-database" class="database-badge">CAPA</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                    </div>

                    <!-- User Dropdown Menu -->
                    <div class="user-dropdown-menu" id="user-dropdown-menu">
                        <div class="dropdown-header">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="user-info-details">
                                <strong id="dropdown-username">Usuario</strong>
                                <small id="dropdown-email"><EMAIL></small>
                            </div>
                        </div>

                        <div class="dropdown-divider"></div>

                        <div class="session-info">
                            <h4><i class="fas fa-info-circle"></i> Información de Sesión</h4>
                            <div class="session-details" id="session-details">
                                <div class="session-item">
                                    <span class="label">Usuario:</span>
                                    <span class="value" id="session-username">-</span>
                                </div>
                                <div class="session-item">
                                    <span class="label">Base de Datos:</span>
                                    <span class="value" id="session-database">-</span>
                                </div>
                                <div class="session-item">
                                    <span class="label">Servidor:</span>
                                    <span class="value" id="session-server">-</span>
                                </div>
                                <div class="session-item">
                                    <span class="label">ID de Sesión:</span>
                                    <span class="value" id="session-id">-</span>
                                </div>
                                <div class="session-item">
                                    <span class="label">Hora de Inicio:</span>
                                    <span class="value" id="session-login-time">-</span>
                                </div>
                                <div class="session-item">
                                    <span class="label">Última Actividad:</span>
                                    <span class="value" id="session-last-activity">-</span>
                                </div>
                                <div class="session-item">
                                    <span class="label">Permisos:</span>
                                    <span class="value" id="session-permissions">-</span>
                                </div>
                            </div>
                        </div>

                        <div class="dropdown-divider"></div>

                        <div class="dropdown-actions">
                            <button class="dropdown-action-btn" id="refresh-session-btn">
                                <i class="fas fa-sync-alt"></i>
                                Actualizar Sesión
                            </button>
                            <button class="dropdown-action-btn logout-action" id="logout-btn">
                                <i class="fas fa-sign-out-alt"></i>
                                Cerrar Sesión
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Contenedor del cuerpo de la aplicación -->
        <div class="app-body">
            <!-- Menú de Navegación -->
            <nav class="main-nav">
                <ul class="nav-menu">
                    <li class="nav-item" data-module="sales">
                        <i class="fas fa-shopping-cart"></i>
                        <span>Ventas</span>
                    </li>
                    <li class="nav-item" data-module="purchasing">
                        <i class="fas fa-shopping-bag"></i>
                        <span>Compras</span>
                    </li>
                    <li class="nav-item" data-module="inventory">
                        <i class="fas fa-boxes"></i>
                        <span>Inventario</span>
                    </li>
                    <li class="nav-item" data-module="production">
                        <i class="fas fa-industry"></i>
                        <span>Producción</span>
                    </li>
                    <li class="nav-item" data-module="partners">
                        <i class="fas fa-users"></i>
                        <span>Socios de Negocio</span>
                    </li>
                    <li class="nav-item" data-module="service">
                        <i class="fas fa-tools"></i>
                        <span>Servicio</span>
                    </li>
                    <li class="nav-item" data-module="banking">
                        <i class="fas fa-university"></i>
                        <span>Bancos</span>
                    </li>
                    <li class="nav-item" data-module="fondos-fijos">
                        <i class="fas fa-wallet"></i>
                        <span>Fondos Fijos</span>
                    </li>
                    <li class="nav-item" data-module="financials">
                        <i class="fas fa-chart-line"></i>
                        <span>Finanzas</span>
                    </li>
                    <li class="nav-item" data-module="mrp">
                        <i class="fas fa-project-diagram"></i>
                        <span>MRP</span>
                    </li>
                    <li class="nav-item" data-module="crm">
                        <i class="fas fa-handshake"></i>
                        <span>CRM</span>
                    </li>
                    <li class="nav-item" data-module="projects">
                        <i class="fas fa-tasks"></i>
                        <span>Proyectos</span>
                    </li>
                    <li class="nav-item" data-module="hr">
                        <i class="fas fa-user-tie"></i>
                        <span>Recursos Humanos</span>
                    </li>
                    <li class="nav-item" data-module="assets">
                        <i class="fas fa-building"></i>
                        <span>Activos Fijos</span>
                    </li>
                    <li class="nav-item" data-module="reports">
                        <i class="fas fa-file-alt"></i>
                        <span>Informes</span>
                    </li>
                    <li class="nav-item" data-module="administration">
                        <i class="fas fa-cog"></i>
                        <span>Administración</span>
                    </li>
                </ul>
            </nav>

            <!-- Área de Contenido -->
            <main class="content-area" id="content-area">
            <div class="dashboard" id="dashboard">
                <div class="dashboard-header">
                    <h1>Panel de Control - SAP Business One</h1>
                    <div class="dashboard-controls">
                        <button class="btn btn-secondary" id="refresh-dashboard">
                            <i class="fas fa-sync-alt"></i>
                            Actualizar
                        </button>
                        <button class="btn btn-secondary" id="fullscreen-dashboard">
                            <i class="fas fa-expand"></i>
                            Pantalla Completa
                        </button>
                    </div>
                </div>

                <div class="dashboard-widgets">
                    <!-- Acciones Rápidas -->
                    <div class="widget quick-actions-widget">
                        <div class="widget-header">
                            <h3><i class="fas fa-bolt"></i> Acciones Rápidas</h3>
                        </div>
                        <div class="widget-content">
                            <div class="quick-actions-grid">
                                <button class="quick-btn" data-action="sales-order">
                                    <i class="fas fa-plus"></i>
                                    <span>Nueva Orden de Venta</span>
                                </button>
                                <button class="quick-btn" data-action="purchase-order">
                                    <i class="fas fa-plus"></i>
                                    <span>Nueva Orden de Compra</span>
                                </button>
                                <button class="quick-btn" data-action="item-master">
                                    <i class="fas fa-plus"></i>
                                    <span>Nuevo Artículo</span>
                                </button>
                                <button class="quick-btn" data-action="business-partner">
                                    <i class="fas fa-plus"></i>
                                    <span>Nuevo Socio de Negocio</span>
                                </button>
                                <button class="quick-btn" data-action="payment">
                                    <i class="fas fa-plus"></i>
                                    <span>Nuevo Pago</span>
                                </button>
                                <button class="quick-btn" data-action="journal-entry">
                                    <i class="fas fa-plus"></i>
                                    <span>Asiento Contable</span>
                                </button>
                                <button class="quick-btn" data-action="production-order">
                                    <i class="fas fa-plus"></i>
                                    <span>Orden de Producción</span>
                                </button>
                                <button class="quick-btn" data-action="service-call">
                                    <i class="fas fa-plus"></i>
                                    <span>Llamada de Servicio</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Estado del Sistema -->
                    <div class="widget system-status-widget">
                        <div class="widget-header">
                            <h3><i class="fas fa-server"></i> Estado del Sistema</h3>
                        </div>
                        <div class="widget-content">
                            <div class="status-grid">
                                <div class="status-item">
                                    <div class="status-icon connected">
                                        <i class="fas fa-plug"></i>
                                    </div>
                                    <div class="status-info">
                                        <span class="status-label">Conexión SAP</span>
                                        <span class="status-value connected" id="sap-status">Conectado</span>
                                    </div>
                                </div>
                                <div class="status-item">
                                    <div class="status-icon">
                                        <i class="fas fa-database"></i>
                                    </div>
                                    <div class="status-info">
                                        <span class="status-label">Base de Datos</span>
                                        <span class="status-value" id="db-status">CAPA</span>
                                    </div>
                                </div>
                                <div class="status-item">
                                    <div class="status-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="status-info">
                                        <span class="status-label">Usuarios Activos</span>
                                        <span class="status-value" id="active-users">1</span>
                                    </div>
                                </div>
                                <div class="status-item">
                                    <div class="status-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="status-info">
                                        <span class="status-label">Última Actualización</span>
                                        <span class="status-value" id="last-update">Ahora</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Métricas de Negocio -->
                    <div class="widget metrics-widget">
                        <div class="widget-header">
                            <h3><i class="fas fa-chart-bar"></i> Métricas de Negocio</h3>
                            <div class="widget-controls">
                                <select id="metrics-period">
                                    <option value="today">Hoy</option>
                                    <option value="week">Esta Semana</option>
                                    <option value="month" selected>Este Mes</option>
                                    <option value="quarter">Este Trimestre</option>
                                </select>
                            </div>
                        </div>
                        <div class="widget-content">
                            <div class="metrics-grid">
                                <div class="metric-card">
                                    <div class="metric-icon sales">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <div class="metric-info">
                                        <span class="metric-label">Ventas del Mes</span>
                                        <span class="metric-value" id="sales-metric">$0</span>
                                        <span class="metric-change positive">+0%</span>
                                    </div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-icon orders">
                                        <i class="fas fa-file-invoice"></i>
                                    </div>
                                    <div class="metric-info">
                                        <span class="metric-label">Órdenes Pendientes</span>
                                        <span class="metric-value" id="orders-metric">0</span>
                                        <span class="metric-change">0 nuevas</span>
                                    </div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-icon inventory">
                                        <i class="fas fa-boxes"></i>
                                    </div>
                                    <div class="metric-info">
                                        <span class="metric-label">Items en Stock</span>
                                        <span class="metric-value" id="inventory-metric">0</span>
                                        <span class="metric-change warning">0 bajo mínimo</span>
                                    </div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-icon customers">
                                        <i class="fas fa-user-friends"></i>
                                    </div>
                                    <div class="metric-info">
                                        <span class="metric-label">Clientes Activos</span>
                                        <span class="metric-value" id="customers-metric">0</span>
                                        <span class="metric-change">0 nuevos</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        </div> <!-- End app-body -->
    </div>

    <!-- Superposición de Carga -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="spinner"></div>
        <p>Cargando...</p>
    </div>

    <!-- Scripts -->
    <script src="assets/js/core/app.js"></script>
    <script src="assets/js/core/auth.js"></script>
    <script src="assets/js/core/api.js"></script>
    <script src="assets/js/core/navigation.js"></script>
    <script src="assets/js/core/dashboard.js"></script>
    <script src="assets/js/modules/sales.js"></script>
    <script src="assets/js/modules/purchasing.js"></script>
    <script src="assets/js/modules/inventory.js"></script>
    <script src="assets/js/modules/production.js"></script>
    <script src="assets/js/modules/partners.js"></script>
    <script src="assets/js/modules/service.js"></script>
    <script src="assets/js/modules/banking.js"></script>
    <script src="assets/js/modules/fondos-fijos.js"></script>
    <script src="assets/js/modules/financials.js"></script>
    <script src="assets/js/modules/mrp.js"></script>
    <script src="assets/js/modules/crm.js"></script>
    <script src="assets/js/modules/projects.js"></script>
    <script src="assets/js/modules/hr.js"></script>
    <script src="assets/js/modules/assets.js"></script>
    <script src="assets/js/modules/reports.js"></script>
    <script src="assets/js/modules/administration.js"></script>
    
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            App.init();
        });
    </script>
</body>
</html>
